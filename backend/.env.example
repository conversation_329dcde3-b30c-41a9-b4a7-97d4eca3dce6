# ==============================================
# 🔧 CONFIGURATION ENVIRONNEMENT BACKEND
# ==============================================

# 🗄️ Base de données Neon PostgreSQL
DATABASE_URL=postgresql://username:password@host:port/database?sslmode=require

# ⚡ Configuration serveur
PORT=5000
NODE_ENV=development

# 🔐 JWT Authentication
JWT_SECRET=your_super_secret_jwt_key_change_this_in_production_minimum_32_chars
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=your_refresh_token_secret
JWT_REFRESH_EXPIRES_IN=30d

# ☁️ Cloudinary (pour les images)
CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret

# 💳 Stripe (pour les paiements)
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# 📧 Email (Nodemailer)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password
EMAIL_FROM=<EMAIL>

# 🌐 URLs
CLIENT_URL=http://localhost:3000
SERVER_URL=http://localhost:5000

# 🔒 Sécurité
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
BCRYPT_SALT_ROUNDS=12

# 📊 Monitoring & Logs
LOG_LEVEL=info
SENTRY_DSN=your_sentry_dsn_for_error_tracking

# 🧪 Tests
TEST_DATABASE_URL=postgresql://username:password@host:port/test_database?sslmode=require

# 📱 Notifications (optionnel)
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=+**********

# 🚀 Production uniquement
REDIS_URL=redis://localhost:6379
CDN_URL=https://your-cdn-domain.com
