{"name": "drone-delivery-backend", "version": "1.0.0", "description": "Backend API pour l'application de vente de drones", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "cross-env NODE_ENV=test jest", "test:watch": "cross-env NODE_ENV=test jest --watch", "test:coverage": "cross-env NODE_ENV=test jest --coverage", "test:integration": "cross-env NODE_ENV=test jest tests/integration", "test:unit": "cross-env NODE_ENV=test jest tests/unit", "migrate": "node src/database/migrate.js", "seed": "node src/database/seed.js"}, "keywords": ["drone", "ecommerce", "api", "nodejs", "express"], "author": "DroneShop", "license": "MIT", "dependencies": {"@neondatabase/serverless": "^0.9.0", "bcryptjs": "^2.4.3", "cloudinary": "^1.41.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "stripe": "^14.7.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "uuid": "^9.0.1"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/supertest": "^6.0.3", "cross-env": "^7.0.3", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}}