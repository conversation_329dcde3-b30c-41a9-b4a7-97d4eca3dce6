const multer = require('multer');
const path = require('path');
const fs = require('fs');
const sharp = require('sharp');

/**
 * Configuration Multer pour l'upload d'images
 * Gestion automatique du redimensionnement et de l'optimisation
 */

// Configuration du stockage
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadPath = path.join(__dirname, '../../uploads');
    
    // Créer le dossier s'il n'existe pas
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }
    
    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    // Générer un nom unique
    const uniqueName = `${Date.now()}-${Math.round(Math.random() * 1E9)}${path.extname(file.originalname)}`;
    cb(null, uniqueName);
  }
});

// Filtrage des fichiers
const fileFilter = (req, file, cb) => {
  // Vérifier le type MIME
  if (file.mimetype.startsWith('image/')) {
    cb(null, true);
  } else {
    cb(new Error('Seuls les fichiers image sont autorisés'), false);
  }
};

// Configuration Multer
const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB max
    files: 5 // 5 fichiers max par upload
  }
});

/**
 * Middleware pour optimiser les images après upload
 */
const optimizeImages = async (req, res, next) => {
  if (!req.files && !req.file) {
    return next();
  }
  
  try {
    const files = req.files || [req.file];
    const optimizedFiles = [];
    
    for (const file of files) {
      const optimized = await processImageFile(file);
      optimizedFiles.push(optimized);
    }
    
    // Ajouter les fichiers optimisés à la requête
    req.optimizedFiles = optimizedFiles;
    next();
    
  } catch (error) {
    console.error('Erreur lors de l\'optimisation:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors du traitement des images'
    });
  }
};

/**
 * Traitement d'un fichier image
 */
async function processImageFile(file) {
  const inputPath = file.path;
  const filename = path.parse(file.filename).name;
  const outputDir = path.join(path.dirname(inputPath), 'optimized');
  
  // Créer le dossier optimized
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }
  
  const variants = {
    thumbnail: { width: 200, height: 200 },
    medium: { width: 400, height: 300 },
    large: { width: 800, height: 600 }
  };
  
  const optimizedVariants = {};
  
  for (const [variant, dimensions] of Object.entries(variants)) {
    const outputPath = path.join(outputDir, `${filename}-${variant}.webp`);
    
    await sharp(inputPath)
      .resize(dimensions.width, dimensions.height, {
        fit: 'cover',
        position: 'center'
      })
      .webp({ quality: 85, effort: 6 })
      .toFile(outputPath);
    
    optimizedVariants[variant] = {
      path: outputPath,
      url: `/uploads/optimized/${filename}-${variant}.webp`,
      width: dimensions.width,
      height: dimensions.height
    };
  }
  
  // Supprimer le fichier original
  fs.unlinkSync(inputPath);
  
  return {
    originalName: file.originalname,
    variants: optimizedVariants,
    size: file.size,
    mimeType: file.mimetype
  };
}

/**
 * Middleware de validation des images
 */
const validateImages = (req, res, next) => {
  const errors = [];
  
  if (!req.files && !req.file) {
    errors.push('Aucun fichier fourni');
  }
  
  const files = req.files || [req.file];
  
  files.forEach((file, index) => {
    // Vérifier la taille
    if (file.size > 10 * 1024 * 1024) {
      errors.push(`Fichier ${index + 1}: Taille trop importante (max 10MB)`);
    }
    
    // Vérifier le format
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.mimetype)) {
      errors.push(`Fichier ${index + 1}: Format non supporté`);
    }
  });
  
  if (errors.length > 0) {
    return res.status(400).json({
      success: false,
      message: 'Erreurs de validation',
      errors
    });
  }
  
  next();
};

/**
 * Route pour l'upload d'images de drones
 */
const uploadDroneImages = [
  upload.array('images', 5),
  validateImages,
  optimizeImages,
  async (req, res) => {
    try {
      const { optimizedFiles } = req;
      
      res.json({
        success: true,
        message: 'Images uploadées et optimisées avec succès',
        files: optimizedFiles
      });
      
    } catch (error) {
      console.error('Erreur upload:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur lors de l\'upload'
      });
    }
  }
];

/**
 * Route pour l'upload d'avatar utilisateur
 */
const uploadAvatar = [
  upload.single('avatar'),
  validateImages,
  optimizeImages,
  async (req, res) => {
    try {
      const { optimizedFiles } = req;
      const avatarFile = optimizedFiles[0];
      
      res.json({
        success: true,
        message: 'Avatar uploadé avec succès',
        avatar: avatarFile.variants.medium // Utiliser la taille moyenne pour l'avatar
      });
      
    } catch (error) {
      console.error('Erreur upload avatar:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de l\'upload de l\'avatar'
      });
    }
  }
];

module.exports = {
  upload,
  optimizeImages,
  validateImages,
  uploadDroneImages,
  uploadAvatar,
  processImageFile
};
