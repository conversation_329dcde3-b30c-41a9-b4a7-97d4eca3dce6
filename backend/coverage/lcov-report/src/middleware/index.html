
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/middleware</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> src/middleware</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">55.12% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>43/78</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">32.55% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>14/43</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">77.77% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>7/9</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">55.26% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>42/76</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="auth.js"><a href="auth.js.html">auth.js</a></td>
	<td data-value="69.23" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 69%"></div><div class="cover-empty" style="width: 31%"></div></div>
	</td>
	<td data-value="69.23" class="pct medium">69.23%</td>
	<td data-value="39" class="abs medium">27/39</td>
	<td data-value="55" class="pct medium">55%</td>
	<td data-value="20" class="abs medium">11/20</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="4" class="abs high">4/4</td>
	<td data-value="69.23" class="pct medium">69.23%</td>
	<td data-value="39" class="abs medium">27/39</td>
	</tr>

<tr>
	<td class="file low" data-value="errorHandler.js"><a href="errorHandler.js.html">errorHandler.js</a></td>
	<td data-value="8" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 8%"></div><div class="cover-empty" style="width: 92%"></div></div>
	</td>
	<td data-value="8" class="pct low">8%</td>
	<td data-value="25" class="abs low">2/25</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="20" class="abs low">0/20</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="2" class="abs low">0/2</td>
	<td data-value="8.33" class="pct low">8.33%</td>
	<td data-value="24" class="abs low">2/24</td>
	</tr>

<tr>
	<td class="file high" data-value="validation.js"><a href="validation.js.html">validation.js</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="14" class="abs high">14/14</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="3" class="abs high">3/3</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="3" class="abs high">3/3</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="13" class="abs high">13/13</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-05-31T02:21:29.858Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    