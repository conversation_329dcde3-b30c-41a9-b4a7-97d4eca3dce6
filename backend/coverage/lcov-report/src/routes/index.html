
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/routes</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> src/routes</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">36.14% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>163/451</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">38.29% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>90/235</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">30.55% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>11/36</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">36.86% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>160/434</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="auth.js"><a href="auth.js.html">auth.js</a></td>
	<td data-value="71.66" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 71%"></div><div class="cover-empty" style="width: 29%"></div></div>
	</td>
	<td data-value="71.66" class="pct medium">71.66%</td>
	<td data-value="60" class="abs medium">43/60</td>
	<td data-value="64.28" class="pct medium">64.28%</td>
	<td data-value="14" class="abs medium">9/14</td>
	<td data-value="66.66" class="pct medium">66.66%</td>
	<td data-value="6" class="abs medium">4/6</td>
	<td data-value="71.66" class="pct medium">71.66%</td>
	<td data-value="60" class="abs medium">43/60</td>
	</tr>

<tr>
	<td class="file low" data-value="categories.js"><a href="categories.js.html">categories.js</a></td>
	<td data-value="14.1" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 14%"></div><div class="cover-empty" style="width: 86%"></div></div>
	</td>
	<td data-value="14.1" class="pct low">14.1%</td>
	<td data-value="78" class="abs low">11/78</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="27" class="abs low">0/27</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="7" class="abs low">0/7</td>
	<td data-value="15.06" class="pct low">15.06%</td>
	<td data-value="73" class="abs low">11/73</td>
	</tr>

<tr>
	<td class="file high" data-value="drones.js"><a href="drones.js.html">drones.js</a></td>
	<td data-value="80.21" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 80%"></div><div class="cover-empty" style="width: 20%"></div></div>
	</td>
	<td data-value="80.21" class="pct high">80.21%</td>
	<td data-value="91" class="abs high">73/91</td>
	<td data-value="66.94" class="pct medium">66.94%</td>
	<td data-value="121" class="abs medium">81/121</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="7" class="abs high">7/7</td>
	<td data-value="79.54" class="pct medium">79.54%</td>
	<td data-value="88" class="abs medium">70/88</td>
	</tr>

<tr>
	<td class="file low" data-value="orders.js"><a href="orders.js.html">orders.js</a></td>
	<td data-value="15.49" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 15%"></div><div class="cover-empty" style="width: 85%"></div></div>
	</td>
	<td data-value="15.49" class="pct low">15.49%</td>
	<td data-value="71" class="abs low">11/71</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="17" class="abs low">0/17</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="4" class="abs low">0/4</td>
	<td data-value="15.71" class="pct low">15.71%</td>
	<td data-value="70" class="abs low">11/70</td>
	</tr>

<tr>
	<td class="file low" data-value="reviews.js"><a href="reviews.js.html">reviews.js</a></td>
	<td data-value="16.92" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 16%"></div><div class="cover-empty" style="width: 84%"></div></div>
	</td>
	<td data-value="16.92" class="pct low">16.92%</td>
	<td data-value="65" class="abs low">11/65</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="20" class="abs low">0/20</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="6" class="abs low">0/6</td>
	<td data-value="17.46" class="pct low">17.46%</td>
	<td data-value="63" class="abs low">11/63</td>
	</tr>

<tr>
	<td class="file low" data-value="users.js"><a href="users.js.html">users.js</a></td>
	<td data-value="16.27" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 16%"></div><div class="cover-empty" style="width: 84%"></div></div>
	</td>
	<td data-value="16.27" class="pct low">16.27%</td>
	<td data-value="86" class="abs low">14/86</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="36" class="abs low">0/36</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="6" class="abs low">0/6</td>
	<td data-value="17.5" class="pct low">17.5%</td>
	<td data-value="80" class="abs low">14/80</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-05-31T02:21:29.858Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    