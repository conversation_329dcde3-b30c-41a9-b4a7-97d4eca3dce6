# Variables d'environnement pour les tests
NODE_ENV=test
PORT=5001

# Base de données de test (utilisera la même que dev pour le moment)
DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require

# JWT pour les tests
JWT_SECRET=test_jwt_secret_for_testing_only_do_not_use_in_production
JWT_EXPIRES_IN=1h

# Services externes (mocks)
CLOUDINARY_CLOUD_NAME=test_cloud_name
CLOUDINARY_API_KEY=test_api_key
CLOUDINARY_API_SECRET=test_api_secret

STRIPE_SECRET_KEY=sk_test_mock_key_for_testing
STRIPE_WEBHOOK_SECRET=whsec_mock_webhook_secret

EMAIL_HOST=test-smtp.example.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=test_password
EMAIL_FROM=<EMAIL>

# URLs de test
CLIENT_URL=http://localhost:3000
SERVER_URL=http://localhost:5001

# Sécurité
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000
BCRYPT_SALT_ROUNDS=4
