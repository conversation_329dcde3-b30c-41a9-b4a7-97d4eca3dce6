import React, { useState, useRef } from 'react';
import { useAppSelector } from '../hooks/redux';
import Button from '../components/common/Button';
import Card from '../components/common/Card';
import Toast from '../components/common/Toast';
import {
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Edit,
  Save,
  Camera,
  Shield,
  Bell,
  CreditCard,
  Package,
  Heart,
  Settings,
  LogOut,
  Check,
  X,
  Eye,
  EyeOff,
  Key,
  Trash2,
  Plus,
  Star,
  Clock,
  Truck,
  Award,
  Lock,
  Loader2,
  Menu,
  Search
} from 'lucide-react';

const ProfilePage: React.FC = () => {
  const { user } = useAppSelector(state => state.auth);
  const [activeTab, setActiveTab] = useState('profile');
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);
  
  const [profileData, setProfileData] = useState({
    firstName: user?.firstName || '',
    lastName: user?.lastName || '',
    email: user?.email || '',
    phone: '+33 6 12 34 56 78',
    address: '123 Rue de la Paix, 75001 Paris',
    birthDate: '1990-01-01',
    bio: 'Passionné de technologie et de drones, j\'adore découvrir les dernières innovations.'
  });

  const [securityData, setSecurityData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  const [paymentMethods, setPaymentMethods] = useState([
    {
      id: 1,
      type: 'Visa',
      lastFour: '4242',
      expiry: '12/25',
      isDefault: true
    },
    {
      id: 2,
      type: 'MasterCard',
      lastFour: '5555',
      expiry: '06/26',
      isDefault: false
    }
  ]);

  const [favoriteItems] = useState([
    {
      id: 1,
      name: 'DJI Mavic Air 2',
      price: 799,
      image: '/api/placeholder/200/200',
      rating: 4.8
    },
    {
      id: 2,
      name: 'DJI Mini 3 Pro',
      price: 689,
      image: '/api/placeholder/200/200',
      rating: 4.9
    },
    {
      id: 3,
      name: 'Autel EVO Lite+',
      price: 1299,
      image: '/api/placeholder/200/200',
      rating: 4.7
    }
  ]);

  const [notifications, setNotifications] = useState({
    email: true,
    sms: false,
    push: true,
    marketing: false
  });

  const [stats] = useState({
    totalOrders: 24,
    totalSpent: 2450,
    favoriteItems: 12,
    memberSince: '2023'
  });

  const [searchFavorites, setSearchFavorites] = useState('');
  const [toast, setToast] = useState<{
    type: 'success' | 'error' | 'warning' | 'info';
    message: string;
    isVisible: boolean;
  }>({
    type: 'success',
    message: '',
    isVisible: false
  });
  
  const filteredFavorites = favoriteItems.filter(item =>
    item.name.toLowerCase().includes(searchFavorites.toLowerCase())
  );

  // Toast helper function
  const showToast = (type: 'success' | 'error' | 'warning' | 'info', message: string) => {
    setToast({ type, message, isVisible: true });
  };

  const tabs = [
    { id: 'profile', label: 'Profil', icon: User },
    { id: 'orders', label: 'Commandes', icon: Package },
    { id: 'favorites', label: 'Favoris', icon: Heart },
    { id: 'payments', label: 'Paiements', icon: CreditCard },
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'security', label: 'Sécurité', icon: Shield }
  ];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setProfileData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSecurityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setSecurityData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleNotificationChange = (key: string) => {
    setNotifications(prev => ({
      ...prev,
      [key]: !prev[key as keyof typeof prev]
    }));
  };

  const handleAvatarUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = () => {
        setAvatarPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSave = async () => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setIsEditing(false);
      console.log('Profile saved:', profileData);
      showToast('success', 'Profil mis à jour avec succès');
    } catch (error) {
      console.error('Error saving profile:', error);
      showToast('error', 'Erreur lors de la sauvegarde du profil');
    } finally {
      setIsLoading(false);
    }
  };

  const handlePasswordUpdate = async () => {
    if (securityData.newPassword !== securityData.confirmPassword) {
      alert('Les mots de passe ne correspondent pas');
      return;
    }
    
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setSecurityData({ currentPassword: '', newPassword: '', confirmPassword: '' });
      alert('Mot de passe mis à jour avec succès');
      showToast('success', 'Mot de passe mis à jour avec succès');
    } catch (error) {
      console.error('Error updating password:', error);
      showToast('error', 'Erreur lors de la mise à jour du mot de passe');
    } finally {
      setIsLoading(false);
    }
  };

  const removePaymentMethod = (id: number) => {
    setPaymentMethods(prev => prev.filter(method => method.id !== id));
    showToast('info', 'Méthode de paiement supprimée');
  };

  const setDefaultPaymentMethod = (id: number) => {
    setPaymentMethods(prev => 
      prev.map(method => ({
        ...method,
        isDefault: method.id === id
      }))
    );
    showToast('success', 'Méthode de paiement par défaut mise à jour');
  };

  const removeFavoriteItem = (id: number) => {
    // In a real app, this would call an API
    console.log('Removing favorite item:', id);
    showToast('info', 'Article retiré des favoris');
  };

  const addToCart = (item: any) => {
    // In a real app, this would add the item to cart state/API
    console.log('Adding to cart:', item);
    showToast('success', 'Article ajouté au panier');
  };

  const handleLogout = () => {
    // In a real app, this would call the logout API and clear auth state
    console.log('Logging out...');
  };

  const handleSettings = () => {
    // Navigate to settings page or open settings modal
    console.log('Opening settings...');
  };

  const renderProfileTab = () => (
    <div className="space-y-6">
      {/* Profile Header */}
      <Card className="p-6 bg-gradient-to-br from-blue-50 to-purple-50 border-0 shadow-lg">
        <div className="flex flex-col sm:flex-row items-center sm:items-start space-y-4 sm:space-y-0 sm:space-x-6">
          <div className="relative group">
            {avatarPreview ? (
              <img 
                src={avatarPreview} 
                alt="Avatar" 
                className="w-24 h-24 rounded-full object-cover border-4 border-white shadow-lg"
              />
            ) : (
              <div className="w-24 h-24 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-2xl font-bold shadow-lg">
                {profileData.firstName.charAt(0)}{profileData.lastName.charAt(0)}
              </div>
            )}
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleAvatarUpload}
              className="hidden"
            />
            <button 
              onClick={() => fileInputRef.current?.click()}
              className="absolute -bottom-2 -right-2 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center hover:bg-blue-700 transition-all duration-200 transform hover:scale-110 shadow-lg"
            >
              <Camera className="w-4 h-4" />
            </button>
          </div>
          
          <div className="flex-1 text-center sm:text-left">
            <h1 className="text-2xl font-bold text-gray-900 mb-1">
              {profileData.firstName} {profileData.lastName}
            </h1>
            <p className="text-gray-600 mb-4 flex items-center justify-center sm:justify-start">
              <Mail className="w-4 h-4 mr-2" />
              {profileData.email}
            </p>
            
            {/* Enhanced Stats */}
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 text-center">
              <div className="bg-white rounded-lg p-3 shadow-md hover:shadow-lg transition-shadow">
                <div className="flex items-center justify-center mb-1">
                  <Package className="w-5 h-5 text-blue-600 mr-1" />
                  <div className="text-2xl font-bold text-blue-600">{stats.totalOrders}</div>
                </div>
                <div className="text-sm text-gray-600">Commandes</div>
              </div>
              <div className="bg-white rounded-lg p-3 shadow-md hover:shadow-lg transition-shadow">
                <div className="flex items-center justify-center mb-1">
                  <Award className="w-5 h-5 text-green-600 mr-1" />
                  <div className="text-2xl font-bold text-green-600">{stats.totalSpent}€</div>
                </div>
                <div className="text-sm text-gray-600">Dépensé</div>
              </div>
              <div className="bg-white rounded-lg p-3 shadow-md hover:shadow-lg transition-shadow">
                <div className="flex items-center justify-center mb-1">
                  <Heart className="w-5 h-5 text-purple-600 mr-1" />
                  <div className="text-2xl font-bold text-purple-600">{stats.favoriteItems}</div>
                </div>
                <div className="text-sm text-gray-600">Favoris</div>
              </div>
              <div className="bg-white rounded-lg p-3 shadow-md hover:shadow-lg transition-shadow">
                <div className="flex items-center justify-center mb-1">
                  <Calendar className="w-5 h-5 text-orange-600 mr-1" />
                  <div className="text-2xl font-bold text-orange-600">{stats.memberSince}</div>
                </div>
                <div className="text-sm text-gray-600">Membre depuis</div>
              </div>
            </div>
          </div>

          <div className="flex space-x-2">
            {isEditing ? (
              <>
                <Button 
                  onClick={handleSave} 
                  size="sm" 
                  className="bg-green-600 hover:bg-green-700 shadow-lg hover:shadow-xl transition-all duration-200"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  ) : (
                    <Save className="w-4 h-4 mr-2" />
                  )}
                  Sauvegarder
                </Button>
                <Button 
                  onClick={() => setIsEditing(false)} 
                  variant="outline" 
                  size="sm"
                  className="shadow-md hover:shadow-lg transition-all duration-200"
                >
                  <X className="w-4 h-4 mr-2" />
                  Annuler
                </Button>
              </>
            ) : (
              <Button 
                onClick={() => setIsEditing(true)} 
                size="sm"
                className="shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"
              >
                <Edit className="w-4 h-4 mr-2" />
                Modifier
              </Button>
            )}
          </div>
        </div>
      </Card>

      {/* Profile Information */}
      <Card className="p-6 shadow-lg border-0">
        <h2 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
          <User className="w-6 h-6 mr-2 text-blue-600" />
          Informations personnelles
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="group">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Prénom
            </label>
            <div className="relative">
              <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5 group-focus-within:text-blue-500 transition-colors" />
              <input
                type="text"
                name="firstName"
                value={profileData.firstName}
                onChange={handleInputChange}
                disabled={!isEditing}
                className="w-full pl-11 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-50 disabled:text-gray-500 transition-all duration-200 hover:border-gray-400"
              />
            </div>
          </div>

          <div className="group">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Nom
            </label>
            <div className="relative">
              <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5 group-focus-within:text-blue-500 transition-colors" />
              <input
                type="text"
                name="lastName"
                value={profileData.lastName}
                onChange={handleInputChange}
                disabled={!isEditing}
                className="w-full pl-11 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-50 disabled:text-gray-500 transition-all duration-200 hover:border-gray-400"
              />
            </div>
          </div>

          <div className="group">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Email
            </label>
            <div className="relative">
              <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5 group-focus-within:text-blue-500 transition-colors" />
              <input
                type="email"
                name="email"
                value={profileData.email}
                onChange={handleInputChange}
                disabled={!isEditing}
                className="w-full pl-11 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-50 disabled:text-gray-500 transition-all duration-200 hover:border-gray-400"
              />
            </div>
          </div>

          <div className="group">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Téléphone
            </label>
            <div className="relative">
              <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5 group-focus-within:text-blue-500 transition-colors" />
              <input
                type="tel"
                name="phone"
                value={profileData.phone}
                onChange={handleInputChange}
                disabled={!isEditing}
                className="w-full pl-11 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-50 disabled:text-gray-500 transition-all duration-200 hover:border-gray-400"
              />
            </div>
          </div>

          <div className="md:col-span-2 group">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Adresse
            </label>
            <div className="relative">
              <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5 group-focus-within:text-blue-500 transition-colors" />
              <input
                type="text"
                name="address"
                value={profileData.address}
                onChange={handleInputChange}
                disabled={!isEditing}
                className="w-full pl-11 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-50 disabled:text-gray-500 transition-all duration-200 hover:border-gray-400"
              />
            </div>
          </div>

          <div className="group">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Date de naissance
            </label>
            <div className="relative">
              <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5 group-focus-within:text-blue-500 transition-colors" />
              <input
                type="date"
                name="birthDate"
                value={profileData.birthDate}
                onChange={handleInputChange}
                disabled={!isEditing}
                className="w-full pl-11 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-50 disabled:text-gray-500 transition-all duration-200 hover:border-gray-400"
              />
            </div>
          </div>

          <div className="md:col-span-2 group">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Bio
            </label>
            <textarea
              name="bio"
              value={profileData.bio}
              onChange={handleInputChange}
              disabled={!isEditing}
              rows={3}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-50 disabled:text-gray-500 transition-all duration-200 hover:border-gray-400"
              placeholder="Parlez-nous de vous..."
            />
          </div>
        </div>
      </Card>
    </div>
  );

  const renderNotificationsTab = () => (
    <Card className="p-6 shadow-lg border-0">
      <h2 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
        <Bell className="w-6 h-6 mr-2 text-blue-600" />
        Préférences de notification
      </h2>
      
      <div className="space-y-4">
        {Object.entries(notifications).map(([key, value]) => {
          const labels = {
            email: 'Notifications par email',
            sms: 'Notifications par SMS',
            push: 'Notifications push',
            marketing: 'Emails marketing'
          };
          
          const descriptions = {
            email: 'Recevez des notifications importantes par email',
            sms: 'Recevez des alertes urgentes par SMS',
            push: 'Notifications dans votre navigateur',
            marketing: 'Offres spéciales et nouveautés'
          };

          const icons = {
            email: Mail,
            sms: Phone,
            push: Bell,
            marketing: Settings
          };

          const IconComponent = icons[key as keyof typeof icons];

          return (
            <div key={key} className="flex items-center justify-between p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl border border-gray-200 hover:shadow-md transition-all duration-200">
              <div className="flex items-center space-x-4">
                <div className={`p-2 rounded-lg ${
                  value ? 'bg-blue-100 text-blue-600' : 'bg-gray-200 text-gray-400'
                }`}>
                  <IconComponent className="w-5 h-5" />
                </div>
                <div>
                  <h3 className="font-medium text-gray-900">{labels[key as keyof typeof labels]}</h3>
                  <p className="text-sm text-gray-600">{descriptions[key as keyof typeof descriptions]}</p>
                </div>
              </div>
              <button
                onClick={() => handleNotificationChange(key)}
                className={`relative w-14 h-7 rounded-full transition-all duration-300 ${
                  value ? 'bg-blue-600 shadow-lg' : 'bg-gray-300'
                } hover:shadow-xl`}
              >
                <div
                  className={`absolute w-6 h-6 bg-white rounded-full shadow-md transition-all duration-300 top-0.5 flex items-center justify-center ${
                    value ? 'translate-x-7' : 'translate-x-0.5'
                  }`}
                >
                  {value && <Check className="w-3 h-3 text-blue-600" />}
                </div>
              </button>
            </div>
          );
        })}
      </div>
      
      <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
        <div className="flex items-start space-x-3">
          <Bell className="w-5 h-5 text-blue-600 mt-0.5" />
          <div>
            <h4 className="font-medium text-blue-900">Conseil</h4>
            <p className="text-sm text-blue-700">
              Activez les notifications email pour ne jamais manquer les mises à jour importantes sur vos commandes.
            </p>
          </div>
        </div>
      </div>
    </Card>
  );

  const renderOrdersTab = () => (
    <Card className="p-6 shadow-lg border-0">
      <h2 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
        <Package className="w-6 h-6 mr-2 text-blue-600" />
        Mes commandes
      </h2>
      
      <div className="space-y-4">
        {[
          { id: 1, date: '2024-01-15', status: 'Livrée', amount: 799, items: 'DJI Mavic Air 2', trackingId: 'DRN789456123' },
          { id: 2, date: '2024-01-10', status: 'En transit', amount: 689, items: 'DJI Mini 3 Pro + Accessoires', trackingId: 'DRN789456124' },
          { id: 3, date: '2024-01-05', status: 'Livrée', amount: 1299, items: 'Autel EVO Lite+', trackingId: 'DRN789456125' }
        ].map((order) => {
          const statusColors = {
            'Livrée': 'bg-green-100 text-green-800 border-green-200',
            'En transit': 'bg-blue-100 text-blue-800 border-blue-200',
            'En préparation': 'bg-yellow-100 text-yellow-800 border-yellow-200',
            'Annulée': 'bg-red-100 text-red-800 border-red-200'
          };

          const statusIcons = {
            'Livrée': Check,
            'En transit': Truck,
            'En préparation': Clock,
            'Annulée': X
          };

          const StatusIcon = statusIcons[order.status as keyof typeof statusIcons] || Package;

          return (
            <div key={order.id} className="border border-gray-200 rounded-xl p-6 hover:shadow-lg transition-all duration-200 bg-gradient-to-r from-white to-gray-50">
              <div className="flex flex-col sm:flex-row sm:items-center justify-between space-y-4 sm:space-y-0">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h3 className="font-semibold text-gray-900">Commande #DR{2024000 + order.id}</h3>
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${statusColors[order.status as keyof typeof statusColors]}`}>
                      <StatusIcon className="w-3 h-3 mr-1" />
                      {order.status}
                    </span>
                  </div>
                  <p className="text-gray-600 mb-1">{order.items}</p>
                  <div className="flex items-center space-x-4 text-sm text-gray-500">
                    <span className="flex items-center">
                      <Calendar className="w-4 h-4 mr-1" />
                      {new Date(order.date).toLocaleDateString('fr-FR')}
                    </span>
                    <span>ID: {order.trackingId}</span>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="text-right">
                    <p className="text-2xl font-bold text-gray-900">{order.amount}€</p>
                  </div>
                  <div className="flex space-x-2">
                    <Button size="sm" variant="outline" className="hover:bg-blue-50">
                      Voir détails
                    </Button>
                    {order.status === 'En transit' && (
                      <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                        <Truck className="w-4 h-4 mr-1" />
                        Suivre
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>
      
      <div className="mt-6 text-center">
        <Button variant="outline" className="hover:bg-gray-50">
          Voir toutes les commandes
        </Button>
      </div>
    </Card>
  );

  const renderFavoritesTab = () => (
    <Card className="p-6 shadow-lg border-0">
      <h2 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
        <Heart className="w-6 h-6 mr-2 text-red-500" />
        Mes favoris
      </h2>
      
      {/* Search Bar */}
      <div className="mb-4">
        <div className="relative">
          <input
            type="text"
            placeholder="Rechercher dans les favoris..."
            value={searchFavorites}
            onChange={(e) => setSearchFavorites(e.target.value)}
            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
            <Search className="w-5 h-5 text-gray-400" />
          </div>
        </div>
      </div>
      
      {filteredFavorites.length > 0 ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredFavorites.map((item) => (
            <div key={item.id} className="bg-white border border-gray-200 rounded-xl p-4 hover:shadow-lg transition-all duration-200 group">
              <div className="relative">
                <img 
                  src={item.image} 
                  alt={item.name}
                  className="w-full h-48 object-cover rounded-lg mb-4"
                />
                <button 
                  onClick={() => removeFavoriteItem(item.id)}
                  className="absolute top-2 right-2 p-2 bg-white rounded-full shadow-md hover:bg-red-50 transition-colors"
                >
                  <Heart className="w-4 h-4 text-red-500 fill-current" />
                </button>
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">{item.name}</h3>
              <div className="flex items-center justify-between mb-3">
                <span className="text-2xl font-bold text-blue-600">{item.price}€</span>
                <div className="flex items-center space-x-1">
                  <Star className="w-4 h-4 text-yellow-400 fill-current" />
                  <span className="text-sm text-gray-600">{item.rating}</span>
                </div>
              </div>
              <Button 
                size="sm" 
                className="w-full group-hover:bg-blue-700 transition-colors"
                onClick={() => addToCart(item)}
              >
                Ajouter au panier
              </Button>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <Heart className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Aucun favori</h3>
          <p className="text-gray-600 mb-6">Vous n'avez pas encore ajouté de produits à vos favoris.</p>
          <Button>Découvrir nos drones</Button>
        </div>
      )}
    </Card>
  );

  const renderPaymentsTab = () => (
    <Card className="p-6 shadow-lg border-0">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900 flex items-center">
          <CreditCard className="w-6 h-6 mr-2 text-blue-600" />
          Moyens de paiement
        </h2>
        <Button size="sm" className="bg-green-600 hover:bg-green-700">
          <Plus className="w-4 h-4 mr-2" />
          Ajouter une carte
        </Button>
      </div>
      
      <div className="space-y-4">
        {paymentMethods.map((method) => (
          <div key={method.id} className="bg-gradient-to-r from-gray-50 to-gray-100 border border-gray-200 rounded-xl p-4 hover:shadow-md transition-all duration-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded flex items-center justify-center text-white text-xs font-bold">
                  {method.type === 'Visa' ? 'VISA' : 'MC'}
                </div>
                <div>
                  <div className="flex items-center space-x-2">
                    <h3 className="font-medium text-gray-900">{method.type} •••• {method.lastFour}</h3>
                    {method.isDefault && (
                      <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full">
                        Par défaut
                      </span>
                    )}
                  </div>
                  <p className="text-sm text-gray-600">Expire le {method.expiry}</p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                {!method.isDefault && (
                  <Button 
                    size="sm" 
                    variant="outline"
                    onClick={() => setDefaultPaymentMethod(method.id)}
                    className="text-blue-600 border-blue-200 hover:bg-blue-50"
                  >
                    Définir par défaut
                  </Button>
                )}
                <Button 
                  size="sm" 
                  variant="outline"
                  onClick={() => removePaymentMethod(method.id)}
                  className="text-red-600 border-red-200 hover:bg-red-50"
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        ))}
      </div>
      
      <div className="mt-6 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
        <div className="flex items-start space-x-3">
          <Shield className="w-5 h-5 text-yellow-600 mt-0.5" />
          <div>
            <h4 className="font-medium text-yellow-900">Sécurité</h4>
            <p className="text-sm text-yellow-700">
              Vos informations de paiement sont sécurisées et chiffrées. Nous ne stockons jamais vos données complètes de carte.
            </p>
          </div>
        </div>
      </div>
    </Card>
  );

  const renderSecurityTab = () => (
    <Card className="p-6 shadow-lg border-0">
      <h2 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
        <Shield className="w-6 h-6 mr-2 text-green-600" />
        Sécurité
      </h2>
      
      <div className="space-y-6">
        {/* Password Section */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <Key className="w-5 h-5 mr-2 text-blue-600" />
            Changer le mot de passe
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Mot de passe actuel
              </label>
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type={showPassword ? "text" : "password"}
                  name="currentPassword"
                  value={securityData.currentPassword}
                  onChange={handleSecurityChange}
                  className="w-full pl-11 pr-12 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Entrez votre mot de passe actuel"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                </button>
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Nouveau mot de passe
              </label>
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="password"
                  name="newPassword"
                  value={securityData.newPassword}
                  onChange={handleSecurityChange}
                  className="w-full pl-11 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Nouveau mot de passe"
                />
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Confirmer le mot de passe
              </label>
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="password"
                  name="confirmPassword"
                  value={securityData.confirmPassword}
                  onChange={handleSecurityChange}
                  className="w-full pl-11 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Confirmer le mot de passe"
                />
              </div>
            </div>
          </div>
          
          <Button 
            onClick={handlePasswordUpdate} 
            className="mt-4 bg-blue-600 hover:bg-blue-700"
            disabled={isLoading}
          >
            {isLoading ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <Key className="w-4 h-4 mr-2" />
            )}
            Mettre à jour le mot de passe
          </Button>
        </div>

        {/* Two-Factor Authentication */}
        <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center">
              <Shield className="w-5 h-5 mr-2 text-green-600" />
              Authentification à deux facteurs
            </h3>
            <span className="bg-red-100 text-red-800 text-sm font-medium px-3 py-1 rounded-full">
              Désactivée
            </span>
          </div>
          <p className="text-gray-600 mb-4">
            Renforcez la sécurité de votre compte en activant l'authentification à deux facteurs.
          </p>
          <Button variant="outline" className="border-green-300 text-green-700 hover:bg-green-50">
            Activer 2FA
          </Button>
        </div>

        {/* Login History */}
        <div className="bg-white border border-gray-200 rounded-xl p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <Clock className="w-5 h-5 mr-2 text-gray-600" />
            Historique des connexions
          </h3>
          <div className="space-y-3">
            {[
              { date: '2024-01-15 14:30', location: 'Paris, France', device: 'Chrome sur Windows', current: true },
              { date: '2024-01-14 09:15', location: 'Paris, France', device: 'Safari sur iPhone', current: false },
              { date: '2024-01-13 18:45', location: 'Lyon, France', device: 'Firefox sur macOS', current: false }
            ].map((login, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <div className="flex items-center space-x-2">
                    <p className="font-medium text-gray-900">{login.device}</p>
                    {login.current && (
                      <span className="bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded-full">
                        Session actuelle
                      </span>
                    )}
                  </div>
                  <p className="text-sm text-gray-600">{login.location} • {login.date}</p>
                </div>
                {!login.current && (
                  <Button size="sm" variant="outline" className="text-red-600 border-red-200 hover:bg-red-50">
                    Révoquer
                  </Button>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </Card>
  );

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Mobile Header with Menu Toggle */}
      <div className="lg:hidden mb-6">
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {avatarPreview ? (
                <img 
                  src={avatarPreview} 
                  alt="Avatar" 
                  className="w-10 h-10 rounded-full object-cover border-2 border-blue-200"
                />
              ) : (
                <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                  {profileData.firstName.charAt(0)}{profileData.lastName.charAt(0)}
                </div>
              )}
              <div>
                <h2 className="font-semibold text-gray-900">{profileData.firstName} {profileData.lastName}</h2>
                <p className="text-sm text-gray-600">{tabs.find(tab => tab.id === activeTab)?.label}</p>
              </div>
            </div>
            <button
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <Menu className="w-6 h-6" />
            </button>
          </div>
          
          {/* Mobile Navigation Menu */}
          {mobileMenuOpen && (
            <div className="mt-4 pt-4 border-t border-gray-200 animate-in slide-in-from-top-2 duration-200">
              <nav className="space-y-1">
                {tabs.map((tab) => {
                  const Icon = tab.icon;
                  return (
                    <button
                      key={tab.id}
                      onClick={() => {
                        setActiveTab(tab.id);
                        setMobileMenuOpen(false);
                      }}
                      className={`w-full flex items-center px-3 py-2 text-left rounded-lg transition-colors ${
                        activeTab === tab.id
                          ? 'bg-blue-600 text-white'
                          : 'text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      <Icon className="w-4 h-4 mr-3" />
                      {tab.label}
                    </button>
                  );
                })}
              </nav>
            </div>
          )}
        </Card>
      </div>

      <div className="flex flex-col lg:flex-row gap-8">
        {/* Desktop Sidebar */}
        <div className="hidden lg:block lg:w-64 flex-shrink-0">
          <Card className="p-4 sticky top-8">
            <nav className="space-y-2">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-all duration-200 transform hover:scale-[1.02] ${
                      activeTab === tab.id
                        ? 'bg-blue-600 text-white shadow-lg'
                        : 'text-gray-700 hover:bg-gray-100 hover:shadow-md'
                    }`}
                  >
                    <Icon className="w-5 h-5 mr-3" />
                    {tab.label}
                  </button>
                );
              })}
              
              <hr className="my-4" />
              
              <button 
                className="w-full flex items-center px-4 py-3 text-left rounded-lg text-gray-700 hover:bg-gray-100 transition-all duration-200 transform hover:scale-[1.02]"
                onClick={handleSettings}
              >
                <Settings className="w-5 h-5 mr-3" />
                Paramètres
              </button>
              
              <button 
                className="w-full flex items-center px-4 py-3 text-left rounded-lg text-red-600 hover:bg-red-50 transition-all duration-200 transform hover:scale-[1.02]"
                onClick={handleLogout}
              >
                <LogOut className="w-5 h-5 mr-3" />
                Déconnexion
              </button>
            </nav>
          </Card>
        </div>

        {/* Main Content */}
        <div className="flex-1">
          <div className="animate-in fade-in-50 duration-300">
            {activeTab === 'profile' && renderProfileTab()}
            {activeTab === 'notifications' && renderNotificationsTab()}
            {activeTab === 'orders' && renderOrdersTab()}
            {activeTab === 'favorites' && renderFavoritesTab()}
            {activeTab === 'payments' && renderPaymentsTab()}
            {activeTab === 'security' && renderSecurityTab()}
          </div>
        </div>
      </div>

      {/* Toast Notification */}
      {toast.isVisible && (
        <Toast 
          type={toast.type} 
          message={toast.message} 
          onClose={() => setToast(prev => ({ ...prev, isVisible: false }))}
        />
      )}
    </div>
  );
};

export default ProfilePage;
