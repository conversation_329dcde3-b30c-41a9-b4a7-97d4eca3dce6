import React, { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { 
  Grid, 
  List, 
  Search, 
  SlidersHorizontal, 
  X, 
  ChevronDown,
  Heart,
  Eye,
  ShoppingCart
} from 'lucide-react';
import { useAppDispatch, useAppSelector } from '../hooks/redux';
import { fetchDrones, setFilters } from '../store/slices/droneSlice';
import DroneCard from '../components/drone/DroneCard';
import LoadingSpinner from '../components/common/LoadingSpinner';
import Button from '../components/common/Button';
import Card from '../components/common/Card';
import OptimizedImage from '../components/common/OptimizedImage';

const DronesPage: React.FC = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showFilters, setShowFilters] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  
  const dispatch = useAppDispatch();
  const { drones, loading, error, total, page, totalPages } = useAppSelector(state => state.drones);
  
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 5000]);
  const [selectedBrand, setSelectedBrand] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [sortBy, setSortBy] = useState('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  // Available filter options
  const brands = ['DJI', 'Parrot', 'Yuneec', 'Autel', 'Skydio'];
  const categories = ['Loisir', 'Professionnel', 'Course', 'Caméra'];
  const sortOptions = [
    { value: 'name', label: 'Nom' },
    { value: 'price', label: 'Prix' },
    { value: 'brand', label: 'Marque' },
    { value: 'created_at', label: 'Nouveauté' }
  ];

  useEffect(() => {
    const search = searchParams.get('search') || '';
    const category = searchParams.get('category') || '';
    
    setSearchTerm(search);
    
    dispatch(fetchDrones({ 
      page: 1, 
      limit: 12, 
      search,
      category,
      sortBy,
      sortOrder
    }));
    
    if (search) {
      dispatch(setFilters({ search }));
    }
    if (category) {
      setSelectedCategory(category);
      dispatch(setFilters({ category }));
    }
  }, [dispatch, searchParams, sortBy, sortOrder]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    const newParams = new URLSearchParams(searchParams);
    if (searchTerm) {
      newParams.set('search', searchTerm);
    } else {
      newParams.delete('search');
    }
    setSearchParams(newParams);
  };

  const handleFilterChange = () => {
    const filters: any = {};
    
    if (selectedBrand) filters.brand = selectedBrand;
    if (selectedCategory) filters.category = selectedCategory;
    if (priceRange[0] > 0 || priceRange[1] < 5000) {
      filters.priceRange = priceRange;
    }
    
    dispatch(setFilters(filters));
    dispatch(fetchDrones({ 
      page: 1, 
      limit: 12,
      sortBy,
      sortOrder
    }));
  };

  const handleSortChange = (newSortBy: string) => {
    const newOrder = newSortBy === sortBy && sortOrder === 'asc' ? 'desc' : 'asc';
    setSortBy(newSortBy);
    setSortOrder(newOrder);
    
    dispatch(fetchDrones({ 
      page: 1, 
      limit: 12,
      sortBy: newSortBy,
      sortOrder: newOrder
    }));
  };

  const clearFilters = () => {
    setPriceRange([0, 5000]);
    setSelectedBrand('');
    setSelectedCategory('');
    setSearchParams({});
    dispatch(setFilters({}));
    dispatch(fetchDrones({ page: 1, limit: 12 }));
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Erreur de chargement</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <Button onClick={() => dispatch(fetchDrones({ page: 1, limit: 12 }))}>
            Réessayer
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Enhanced Header with Search */}
      <div className="mb-8">
        <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-6">
          <div>
            <h1 className="text-4xl font-bold text-gray-900 mb-2">
              Catalogue des drones
            </h1>
            <p className="text-gray-600">
              {total} drone{total > 1 ? 's' : ''} disponible{total > 1 ? 's' : ''}
            </p>
          </div>
          
          {/* Enhanced Search Bar */}
          <form onSubmit={handleSearch} className="flex-1 max-w-md">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Rechercher un drone..."
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              {searchTerm && (
                <button
                  type="button"
                  onClick={() => setSearchTerm('')}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  <X className="w-5 h-5" />
                </button>
              )}
            </div>
          </form>
        </div>

        {/* Toolbar */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mt-6">
          <div className="flex items-center space-x-4">
            {/* Filters Toggle */}
            <Button
              variant={showFilters ? "primary" : "outline"}
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center space-x-2"
            >
              <SlidersHorizontal className="w-4 h-4" />
              <span>Filtres</span>
            </Button>

            {/* Sort Dropdown */}
            <div className="relative">
              <select
                value={`${sortBy}-${sortOrder}`}
                onChange={(e) => {
                  const [newSortBy, newOrder] = e.target.value.split('-');
                  setSortBy(newSortBy);
                  setSortOrder(newOrder as 'asc' | 'desc');
                  handleSortChange(newSortBy);
                }}
                className="appearance-none bg-white border border-gray-300 rounded-lg px-4 py-2 pr-8 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {sortOptions.map(option => (
                  <React.Fragment key={option.value}>
                    <option value={`${option.value}-asc`}>{option.label} (A-Z)</option>
                    <option value={`${option.value}-desc`}>{option.label} (Z-A)</option>
                  </React.Fragment>
                ))}
              </select>
              <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4 pointer-events-none" />
            </div>
          </div>

          {/* View Mode Toggle */}
          <div className="flex border border-gray-300 rounded-lg">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-3 ${
                viewMode === 'grid'
                  ? 'bg-blue-500 text-white'
                  : 'bg-white text-gray-600 hover:bg-gray-50'
              } rounded-l-lg transition-colors`}
            >
              <Grid className="h-4 w-4" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-3 ${
                viewMode === 'list'
                  ? 'bg-blue-500 text-white'
                  : 'bg-white text-gray-600 hover:bg-gray-50'
              } rounded-r-lg transition-colors`}
            >
              <List className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Enhanced Filters Panel */}
      {showFilters && (
        <Card className="mb-8" padding="lg">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {/* Category Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Catégorie
              </label>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Toutes les catégories</option>
                {categories.map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>
            </div>

            {/* Brand Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Marque
              </label>
              <select
                value={selectedBrand}
                onChange={(e) => setSelectedBrand(e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Toutes les marques</option>
                {brands.map(brand => (
                  <option key={brand} value={brand}>{brand}</option>
                ))}
              </select>
            </div>

            {/* Price Range */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Prix (€)
              </label>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <input
                    type="number"
                    value={priceRange[0]}
                    onChange={(e) => setPriceRange([+e.target.value, priceRange[1]])}
                    placeholder="Min"
                    className="w-full p-2 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <span className="text-gray-500">-</span>
                  <input
                    type="number"
                    value={priceRange[1]}
                    onChange={(e) => setPriceRange([priceRange[0], +e.target.value])}
                    placeholder="Max"
                    className="w-full p-2 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
            </div>

            {/* Filter Actions */}
            <div className="flex flex-col justify-end space-y-2">
              <Button onClick={handleFilterChange} size="sm">
                Appliquer
              </Button>
              <Button variant="outline" onClick={clearFilters} size="sm">
                Réinitialiser
              </Button>
            </div>
          </div>
        </Card>
      )}

      {/* Main Content Area */}
      <div className="flex flex-col gap-8">
        {/* Drones Grid/List */}
        <div>
          {drones.length === 0 ? (
            <div className="text-center py-12">
              <div className="max-w-md mx-auto">
                <div className="w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center">
                  <Search className="w-12 h-12 text-gray-400" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  Aucun drone trouvé
                </h3>
                <p className="text-gray-600 mb-4">
                  Essayez de modifier vos critères de recherche ou d'ajuster les filtres
                </p>
                <Button onClick={clearFilters} variant="outline">
                  Réinitialiser les filtres
                </Button>
              </div>
            </div>
          ) : (
            <>
              {/* Results Summary */}
              <div className="flex items-center justify-between mb-6">
                <p className="text-sm text-gray-600">
                  Affichage de {drones.length} drone{drones.length > 1 ? 's' : ''} sur {total}
                </p>
                {searchTerm && (
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <span>Recherche pour :</span>
                    <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-md font-medium">
                      "{searchTerm}"
                    </span>
                  </div>
                )}
              </div>

              {/* Drones Display */}
              {viewMode === 'grid' ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {drones.map((drone) => (
                    <div key={drone.id} className="group">
                      <DroneCard drone={drone} />
                    </div>
                  ))}
                </div>
              ) : (
                <div className="space-y-4">
                  {drones.map((drone) => (
                    <Card key={drone.id} className="hover:shadow-lg transition-all duration-300">
                      <div className="flex flex-col sm:flex-row gap-6 p-6">
                        <div className="flex-shrink-0">
                          <img
                            src={drone.images[0] || '/api/placeholder/200/150'}
                            alt={drone.name}
                            className="w-full sm:w-48 h-36 object-cover rounded-lg"
                          />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex justify-between items-start mb-2">
                            <h3 className="text-lg font-semibold text-gray-900 truncate">
                              {drone.name}
                            </h3>
                            <div className="flex items-center space-x-1">
                              <button className="p-2 text-gray-400 hover:text-red-500 transition-colors">
                                <Heart className="w-4 h-4" />
                              </button>
                              <button className="p-2 text-gray-400 hover:text-blue-500 transition-colors">
                                <Eye className="w-4 h-4" />
                              </button>
                            </div>
                          </div>
                          
                          <p className="text-gray-600 text-sm mb-2">{drone.brand}</p>
                          
                          <p className="text-gray-700 mb-4 line-clamp-2">
                            {drone.description}
                          </p>
                          
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-4">
                              <span className="text-2xl font-bold text-blue-600">
                                {drone.price}€
                              </span>
                            </div>
                            
                            <div className="flex items-center space-x-2">
                              <Button variant="outline" size="sm">
                                <Eye className="w-4 h-4" />
                              </Button>
                              <Button size="sm">
                                <ShoppingCart className="w-4 h-4 mr-2" />
                                Ajouter
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              )}
            </>
          )}

          {/* Enhanced Pagination */}
          {totalPages > 1 && (
            <div className="flex flex-col sm:flex-row items-center justify-between mt-12 pt-8 border-t border-gray-200">
              <p className="text-sm text-gray-700 mb-4 sm:mb-0">
                Page {page} sur {totalPages}
              </p>
              
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => dispatch(fetchDrones({ page: page - 1, limit: 12 }))}
                  disabled={page <= 1}
                >
                  Précédent
                </Button>
                
                <div className="flex space-x-1">
                  {Array.from({ length: Math.min(totalPages, 7) }, (_, i) => {
                    let pageNum: number;
                    if (totalPages <= 7) {
                      pageNum = i + 1;
                    } else if (page <= 4) {
                      pageNum = i + 1;
                    } else if (page >= totalPages - 3) {
                      pageNum = totalPages - 6 + i;
                    } else {
                      pageNum = page - 3 + i;
                    }
                    
                    return (
                      <button
                        key={pageNum}
                        onClick={() => dispatch(fetchDrones({ page: pageNum, limit: 12 }))}
                        className={`w-10 h-10 text-sm font-medium rounded-lg transition-colors ${
                          pageNum === page
                            ? 'bg-blue-600 text-white'
                            : 'text-gray-700 bg-white border border-gray-300 hover:bg-gray-50'
                        }`}
                      >
                        {pageNum}
                      </button>
                    );
                  })}
                </div>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => dispatch(fetchDrones({ page: page + 1, limit: 12 }))}
                  disabled={page >= totalPages}
                >
                  Suivant
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DronesPage;
