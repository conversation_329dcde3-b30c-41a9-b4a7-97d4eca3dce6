import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { <PERSON>R<PERSON>, Shield, Truck, Headphones, Star, Zap, Camera, Gamepad2 } from 'lucide-react';
import Button from '../components/common/Button';
import Card from '../components/common/Card';

const HomePage: React.FC = () => {
  return (
    <div>
      {/* Hero Section */}
      <section className="relative overflow-hidden gradient-primary text-white">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0 bg-gradient-to-br from-transparent via-white/5 to-transparent"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 sm:py-20 lg:py-24">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 sm:gap-12 lg:gap-16 items-center">
            <div className="animate-fade-in">
              <div className="inline-flex items-center px-4 py-2 bg-white/10 backdrop-blur-sm rounded-full text-sm font-medium mb-6">
                <Zap className="w-4 h-4 mr-2" />
                Nouvelle collection 2025
              </div>
              <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-black mb-6 sm:mb-8 leading-tight">
                Découvrez l'univers des
                <span className="gradient-text bg-gradient-to-r from-blue-200 to-white bg-clip-text text-transparent"> drones</span>
              </h1>
              <p className="text-base sm:text-lg lg:text-xl mb-8 sm:mb-10 text-blue-100 leading-relaxed">
                De la photographie aérienne aux courses de vitesse, trouvez le drone parfait
                pour vos besoins. Qualité professionnelle, prix compétitifs.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 sm:gap-6">
                <Link to="/drones" className="w-full sm:w-auto">
                  <Button
                    size="xl"
                    variant="secondary"
                    rounded="xl"
                    shadow="xl"
                    className="bg-white text-blue-600 hover:bg-gray-100 font-bold w-full sm:w-auto"
                  >
                    Explorer nos drones
                    <ArrowRight className="ml-2 h-5 w-5 sm:h-6 sm:w-6" />
                  </Button>
                </Link>
                <Button
                  variant="outline"
                  size="xl"
                  rounded="xl"
                  className="border-2 border-white text-white hover:bg-white hover:text-blue-600 font-bold backdrop-blur-sm bg-white/10 w-full sm:w-auto"
                >
                  En savoir plus
                </Button>
              </div>
            </div>
            <div className="relative animate-slide-up">
              <div className="relative">
                <img
                  src="https://via.placeholder.com/600x400"
                  alt="Drone professionnel"
                  className="rounded-3xl shadow-2xl transform hover:scale-105 transition-transform duration-500 w-full"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-3xl"></div>
              </div>

              {/* Floating Stats - Responsive et améliorés */}
              <div className="absolute -bottom-2 -left-2 sm:-bottom-4 sm:-left-4 lg:-bottom-6 lg:-left-6 glass p-4 sm:p-5 lg:p-6 rounded-xl lg:rounded-2xl shadow-2xl animate-bounce-gentle border border-white/20 max-w-[280px] sm:max-w-none">
                <div className="flex items-center space-x-3 sm:space-x-4">
                  <div className="bg-gradient-to-r from-yellow-400 to-orange-500 p-2.5 sm:p-3 rounded-lg lg:rounded-xl flex-shrink-0">
                    <Star className="h-5 w-5 sm:h-6 sm:w-6 text-white fill-current" />
                  </div>
                  <div className="min-w-0 flex-1">
                    <div className="font-black text-xl sm:text-2xl lg:text-3xl text-gray-900 leading-none">4.9</div>
                    <div className="flex items-center space-x-1 my-1">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className="w-3 h-3 sm:w-4 sm:h-4 text-yellow-400 fill-current flex-shrink-0" />
                      ))}
                    </div>
                    <div className="text-xs sm:text-sm text-gray-600 font-medium leading-tight">+1,247 avis clients</div>
                  </div>
                </div>
              </div>

              <div className="absolute -top-2 -right-2 sm:-top-4 sm:-right-4 lg:-top-6 lg:-right-6 glass p-4 sm:p-5 lg:p-6 rounded-xl lg:rounded-2xl shadow-2xl border border-white/20 min-w-[120px] sm:min-w-[140px]">
                <div className="text-center">
                  <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-2.5 sm:p-3 rounded-lg lg:rounded-xl mb-2 lg:mb-3 mx-auto w-fit">
                    <Camera className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
                  </div>
                  <div className="font-black text-2xl sm:text-3xl lg:text-4xl text-blue-600 mb-1 leading-none">500+</div>
                  <div className="text-xs sm:text-sm text-gray-600 font-medium leading-tight">Modèles disponibles</div>
                </div>
              </div>

              {/* Additional floating stat - Hidden on mobile, visible on larger screens */}
              <div className="hidden md:block absolute top-1/2 -left-6 lg:-left-10 glass p-3 lg:p-4 rounded-xl lg:rounded-2xl shadow-xl border border-white/20 animate-pulse min-w-[100px]">
                <div className="text-center">
                  <div className="bg-gradient-to-r from-green-500 to-emerald-600 p-2 lg:p-2.5 rounded-lg mb-2 mx-auto w-fit">
                    <Truck className="h-4 w-4 lg:h-5 lg:w-5 text-white" />
                  </div>
                  <div className="font-bold text-lg lg:text-xl text-gray-900 leading-none">24h</div>
                  <div className="text-xs text-gray-600 leading-tight">Livraison rapide</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-24 gradient-secondary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20 animate-fade-in">
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-black text-gray-900 mb-6">
              Pourquoi choisir <span className="gradient-text">DroneShop</span> ?
            </h2>
            <p className="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Nous nous engageons à vous offrir la meilleure expérience d'achat
              et les produits de la plus haute qualité.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card className="text-center group" hover={true} padding="lg" shadow="lg">
              <div className="bg-gradient-to-br from-blue-500 to-blue-600 w-20 h-20 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                <Shield className="h-10 w-10 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                Garantie qualité
              </h3>
              <p className="text-gray-600 leading-relaxed">
                Tous nos drones sont testés et garantis. Satisfaction garantie ou remboursé.
              </p>
            </Card>

            <Card className="text-center group" hover={true} padding="lg" shadow="lg">
              <div className="bg-gradient-to-br from-green-500 to-green-600 w-20 h-20 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                <Truck className="h-10 w-10 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                Livraison rapide
              </h3>
              <p className="text-gray-600 leading-relaxed">
                Livraison gratuite en 24-48h partout en France métropolitaine.
              </p>
            </Card>

            <Card className="text-center group" hover={true} padding="lg" shadow="lg">
              <div className="bg-gradient-to-br from-purple-500 to-purple-600 w-20 h-20 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                <Headphones className="h-10 w-10 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                Support expert
              </h3>
              <p className="text-gray-600 leading-relaxed">
                Notre équipe d'experts est là pour vous conseiller et vous accompagner.
              </p>
            </Card>
          </div>
        </div>
      </section>

      {/* Categories Section */}
      <section className="py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20 animate-fade-in">
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-black text-gray-900 mb-6">
              Explorez nos <span className="gradient-text">catégories</span>
            </h2>
            <p className="text-lg sm:text-xl text-gray-600 leading-relaxed">
              Trouvez le drone parfait selon votre usage
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
            {[
              {
                title: 'Drones de loisir',
                description: 'Parfaits pour débuter et s\'amuser',
                image: 'https://via.placeholder.com/300x200',
                link: '/drones?category=loisir',
                icon: Gamepad2,
                gradient: 'from-green-500 to-emerald-600'
              },
              {
                title: 'Drones professionnels',
                description: 'Pour la photographie et la vidéographie',
                image: 'https://via.placeholder.com/300x200',
                link: '/drones?category=professionnel',
                icon: Camera,
                gradient: 'from-blue-500 to-indigo-600'
              },
              {
                title: 'Drones de course',
                description: 'Vitesse et agilité pour les compétitions',
                image: 'https://via.placeholder.com/300x200',
                link: '/drones?category=course',
                icon: Zap,
                gradient: 'from-orange-500 to-red-600'
              },
            ].map((category, index) => (
              <Link
                key={index}
                to={category.link}
                className="group block"
              >
                <Card className="overflow-hidden" hover={true} padding="none" shadow="xl">
                  <div className="relative">
                    <img
                      src={category.image}
                      alt={category.title}
                      className="w-full h-48 sm:h-56 object-cover group-hover:scale-110 transition-transform duration-500"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent"></div>
                    <div className={`absolute top-4 right-4 bg-gradient-to-r ${category.gradient} p-2 sm:p-3 rounded-lg sm:rounded-xl shadow-lg`}>
                      <category.icon className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
                    </div>
                  </div>
                  <div className="p-6 sm:p-8">
                    <h3 className="text-xl sm:text-2xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors">
                      {category.title}
                    </h3>
                    <p className="text-gray-600 leading-relaxed text-sm sm:text-base">
                      {category.description}
                    </p>
                    <div className="mt-4 inline-flex items-center text-blue-600 font-semibold group-hover:translate-x-2 transition-transform text-sm sm:text-base">
                      Découvrir
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </div>
                  </div>
                </Card>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-24 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            <h2 className="text-4xl md:text-5xl font-black text-gray-900 mb-6">
              Ce que disent nos <span className="gradient-text">clients</span>
            </h2>
            <p className="text-xl text-gray-600 leading-relaxed">
              Plus de 1000 clients nous font confiance
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                name: "Marie Dubois",
                role: "Photographe professionnelle",
                content: "Excellent service et produits de qualité. Mon drone DJI acheté ici fonctionne parfaitement depuis 2 ans.",
                rating: 5,
                avatar: "https://via.placeholder.com/60x60"
              },
              {
                name: "Pierre Martin",
                role: "Amateur de tech",
                content: "Livraison ultra rapide et support client réactif. Je recommande vivement pour tout achat de drone.",
                rating: 5,
                avatar: "https://via.placeholder.com/60x60"
              },
              {
                name: "Sarah Johnson",
                role: "Pilote FPV",
                content: "Large choix de drones de course et pièces détachées. L'équipe connaît vraiment son domaine.",
                rating: 5,
                avatar: "https://via.placeholder.com/60x60"
              }
            ].map((testimonial, index) => (
              <Card key={index} className="text-center" padding="lg" shadow="lg" hover={true}>
                <div className="flex justify-center mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                  ))}
                </div>
                <p className="text-gray-600 mb-6 italic leading-relaxed">
                  "{testimonial.content}"
                </p>
                <div className="flex items-center justify-center space-x-3">
                  <img
                    src={testimonial.avatar}
                    alt={testimonial.name}
                    className="w-12 h-12 rounded-full"
                  />
                  <div>
                    <div className="font-semibold text-gray-900">{testimonial.name}</div>
                    <div className="text-sm text-gray-500">{testimonial.role}</div>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-24">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            <h2 className="text-4xl md:text-5xl font-black text-gray-900 mb-6">
              Questions <span className="gradient-text">fréquentes</span>
            </h2>
            <p className="text-xl text-gray-600 leading-relaxed">
              Trouvez rapidement les réponses à vos questions
            </p>
          </div>

          <div className="space-y-6">
            {[
              {
                question: "Quelle est la durée de garantie des drones ?",
                answer: "Tous nos drones sont garantis 2 ans pièces et main d'œuvre. Nous proposons également des extensions de garantie."
              },
              {
                question: "Proposez-vous des formations au pilotage ?",
                answer: "Oui, nous organisons des sessions de formation pour débutants et pilotes confirmés. Contactez-nous pour plus d'informations."
              },
              {
                question: "Les frais de livraison sont-ils gratuits ?",
                answer: "La livraison est gratuite en France métropolitaine pour toute commande supérieure à 100€."
              },
              {
                question: "Puis-je retourner un drone si je ne suis pas satisfait ?",
                answer: "Vous disposez de 30 jours pour retourner votre achat en parfait état. Les frais de retour sont à votre charge."
              }
            ].map((faq, index) => (
              <Card key={index} className="group cursor-pointer" padding="lg" hover={true}>
                <details className="group">
                  <summary className="flex justify-between items-center cursor-pointer list-none">
                    <h3 className="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                      {faq.question}
                    </h3>
                    <div className="transform group-open:rotate-45 transition-transform">
                      <ArrowRight className="w-5 h-5" />
                    </div>
                  </summary>
                  <p className="mt-4 text-gray-600 leading-relaxed">
                    {faq.answer}
                  </p>
                </details>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Newsletter Section */}
      <section className="py-24 gradient-secondary">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl md:text-5xl font-black text-gray-900 mb-6">
            Restez informé des <span className="gradient-text">nouveautés</span>
          </h2>
          <p className="text-xl text-gray-600 mb-12 leading-relaxed">
            Recevez en avant-première nos offres exclusives et les dernières actualités du monde des drones
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 max-w-lg mx-auto">
            <input
              type="email"
              placeholder="Votre adresse email"
              className="flex-1 px-6 py-4 rounded-xl border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <Button
              size="lg"
              rounded="xl"
              className="bg-blue-600 hover:bg-blue-700 text-white font-semibold"
            >
              S'abonner
            </Button>
          </div>
          
          <p className="text-sm text-gray-500 mt-4">
            Pas de spam, désinscription possible à tout moment
          </p>
        </div>
      </section>
    </div>
  );
};

export default HomePage;
