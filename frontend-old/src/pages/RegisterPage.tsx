import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../hooks/redux';
import { register, clearError } from '../store/slices/authSlice';
import Button from '../components/common/Button';
import Card from '../components/common/Card';
import { 
  Eye, 
  EyeOff, 
  Check, 
  X, 
  User,
  Mail,
  Lock,
  UserPlus,
  Shield,
  Zap,
  CheckCircle
} from 'lucide-react';

const RegisterPage: React.FC = () => {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    acceptTerms: false,
    newsletter: false,
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { loading, error } = useAppSelector(state => state.auth);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    
    // Clear error when user starts typing
    if (error) {
      dispatch(clearError());
    }
  };

  const validatePassword = (password: string) => {
    const requirements = [
      { test: password.length >= 8, text: 'Au moins 8 caractères' },
      { test: /[A-Z]/.test(password), text: 'Une majuscule' },
      { test: /[a-z]/.test(password), text: 'Une minuscule' },
      { test: /\d/.test(password), text: 'Un chiffre' },
      { test: /[!@#$%^&*(),.?":{}|<>]/.test(password), text: 'Un caractère spécial' },
    ];
    return requirements;
  };

  const passwordRequirements = validatePassword(formData.password);
  const isPasswordValid = passwordRequirements.every(req => req.test);
  const passwordsMatch = formData.password === formData.confirmPassword;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!isPasswordValid || !passwordsMatch || !formData.acceptTerms) {
      return;
    }
    
    setIsAnimating(true);
    
    try {
      await dispatch(register({
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        password: formData.password
      })).unwrap();
      
      // Success animation before redirect
      setTimeout(() => {
        navigate('/');
      }, 1500);
    } catch (error) {
      setIsAnimating(false);
    }
  };

  const handleNextStep = () => {
    if (currentStep === 1 && formData.firstName && formData.lastName && formData.email) {
      setCurrentStep(2);
    }
  };

  const handleSocialRegister = (provider: string) => {
    console.log(`Register with ${provider}`);
    // Implement social registration logic here
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50">
      {/* Background Pattern */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-purple-400 to-blue-600 rounded-full opacity-10 animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-96 h-96 bg-gradient-to-tr from-blue-400 to-purple-600 rounded-full opacity-10 animate-pulse delay-1000"></div>
      </div>

      <div className="relative flex min-h-screen">
        {/* Left Side - Benefits */}
        <div className="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-purple-600 to-blue-700 p-12 flex-col justify-center">
          <div className="max-w-md mx-auto text-white">
            <div className="mb-8">
              <div className="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center mb-6 backdrop-blur-sm">
                <UserPlus className="w-8 h-8 text-white" />
              </div>
              <h1 className="text-4xl font-bold mb-4">
                Rejoignez DroneDelivery
              </h1>
              <p className="text-purple-100 text-lg">
                Découvrez l'avenir de la livraison avec notre technologie révolutionnaire
              </p>
            </div>

            <div className="space-y-6">
              <div className="flex items-start space-x-4">
                <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                  <Zap className="w-4 h-4 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold mb-2">Livraison Express</h3>
                  <p className="text-purple-100 text-sm">
                    Commandes livrées en moins de 30 minutes dans un rayon de 50km
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                  <Shield className="w-4 h-4 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold mb-2">100% Sécurisé</h3>
                  <p className="text-purple-100 text-sm">
                    Technologie de pointe et assurance complète pour toutes vos livraisons
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                  <CheckCircle className="w-4 h-4 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold mb-2">Service Premium</h3>
                  <p className="text-purple-100 text-sm">
                    Support client 24/7 et suivi en temps réel de toutes vos commandes
                  </p>
                </div>
              </div>
            </div>

            {/* Progress Indicator */}
            <div className="mt-12">
              <div className="flex items-center space-x-4">
                <div className={`w-3 h-3 rounded-full transition-colors ${
                  currentStep >= 1 ? 'bg-white' : 'bg-white/30'
                }`}></div>
                <div className={`w-3 h-3 rounded-full transition-colors ${
                  currentStep >= 2 ? 'bg-white' : 'bg-white/30'
                }`}></div>
                <span className="text-purple-100 text-sm ml-2">
                  Étape {currentStep} sur 2
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Right Side - Registration Form */}
        <div className="w-full lg:w-1/2 flex items-center justify-center p-8">
          <div className="w-full max-w-md">
            <Card className="p-8 shadow-xl border-0 backdrop-blur-sm bg-white/95">
              {/* Header */}
              <div className="text-center mb-8">
                <div className="w-16 h-16 bg-gradient-to-br from-purple-600 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
                  <span className="text-white font-bold text-2xl">D</span>
                </div>
                <h2 className="text-3xl font-bold text-gray-900 mb-2">
                  Créer un compte
                </h2>
                <p className="text-gray-600">
                  {currentStep === 1 ? 'Commençons par vos informations personnelles' : 'Sécurisez votre compte'}
                </p>
              </div>

              {/* Progress Bar */}
              <div className="mb-8">
                <div className="flex items-center space-x-2 mb-2">
                  <div className={`h-2 flex-1 rounded-full transition-colors ${
                    currentStep >= 1 ? 'bg-gradient-to-r from-purple-500 to-blue-500' : 'bg-gray-200'
                  }`}></div>
                  <div className={`h-2 flex-1 rounded-full transition-colors ${
                    currentStep >= 2 ? 'bg-gradient-to-r from-purple-500 to-blue-500' : 'bg-gray-200'
                  }`}></div>
                </div>
                <div className="flex justify-between text-xs text-gray-500">
                  <span>Informations</span>
                  <span>Sécurité</span>
                </div>
              </div>

              {/* Form */}
              <form onSubmit={handleSubmit} className="space-y-6">
                {error && (
                  <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg flex items-center space-x-2 animate-shake">
                    <div className="w-5 h-5 bg-red-500 rounded-full flex items-center justify-center flex-shrink-0">
                      <span className="text-white text-xs font-bold">!</span>
                    </div>
                    <span>{error}</span>
                  </div>
                )}

                {currentStep === 1 && (
                  <div className="space-y-4 animate-fadeIn">
                    {/* Name Fields */}
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-2">
                          Prénom
                        </label>
                        <div className="relative group">
                          <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5 group-focus-within:text-purple-500 transition-colors" />
                          <input
                            id="firstName"
                            name="firstName"
                            type="text"
                            required
                            value={formData.firstName}
                            onChange={handleInputChange}
                            className="w-full pl-11 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 hover:border-gray-400"
                            placeholder="John"
                          />
                        </div>
                      </div>
                      
                      <div>
                        <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-2">
                          Nom
                        </label>
                        <div className="relative group">
                          <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5 group-focus-within:text-purple-500 transition-colors" />
                          <input
                            id="lastName"
                            name="lastName"
                            type="text"
                            required
                            value={formData.lastName}
                            onChange={handleInputChange}
                            className="w-full pl-11 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 hover:border-gray-400"
                            placeholder="Doe"
                          />
                        </div>
                      </div>
                    </div>
                    
                    {/* Email Field */}
                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                        Adresse email
                      </label>
                      <div className="relative group">
                        <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5 group-focus-within:text-purple-500 transition-colors" />
                        <input
                          id="email"
                          name="email"
                          type="email"
                          autoComplete="email"
                          required
                          value={formData.email}
                          onChange={handleInputChange}
                          className="w-full pl-11 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 hover:border-gray-400"
                          placeholder="<EMAIL>"
                        />
                      </div>
                    </div>

                    {/* Next Step Button */}
                    <Button
                      type="button"
                      onClick={handleNextStep}
                      fullWidth
                      size="lg"
                      disabled={!formData.firstName || !formData.lastName || !formData.email}
                      className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 transform transition-all duration-200 hover:scale-105"
                    >
                      Continuer
                    </Button>

                    {/* Social Registration */}
                    <div className="relative my-6">
                      <div className="absolute inset-0 flex items-center">
                        <div className="w-full border-t border-gray-300" />
                      </div>
                      <div className="relative flex justify-center text-sm">
                        <span className="px-4 bg-white text-gray-500">Ou inscrivez-vous avec</span>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-3">
                      <button
                        type="button"
                        onClick={() => handleSocialRegister('google')}
                        className="flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg bg-white text-gray-700 hover:bg-gray-50 transition-colors duration-200 hover:scale-105 transform"
                      >
                        <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
                          <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                          <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                          <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                          <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                        </svg>
                        Google
                      </button>
                      
                      <button
                        type="button"
                        onClick={() => handleSocialRegister('facebook')}
                        className="flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg bg-white text-gray-700 hover:bg-gray-50 transition-colors duration-200 hover:scale-105 transform"
                      >
                        <svg className="w-5 h-5 mr-2" fill="#1877F2" viewBox="0 0 24 24">
                          <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                        </svg>
                        Facebook
                      </button>
                    </div>
                  </div>
                )}

                {currentStep === 2 && (
                  <div className="space-y-4 animate-fadeIn">
                    {/* Password Field */}
                    <div>
                      <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                        Mot de passe
                      </label>
                      <div className="relative group">
                        <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5 group-focus-within:text-purple-500 transition-colors" />
                        <input
                          id="password"
                          name="password"
                          type={showPassword ? 'text' : 'password'}
                          required
                          value={formData.password}
                          onChange={handleInputChange}
                          className="w-full pl-11 pr-12 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 hover:border-gray-400"
                          placeholder="Choisissez un mot de passe"
                        />
                        <button
                          type="button"
                          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                          onClick={() => setShowPassword(!showPassword)}
                        >
                          {showPassword ? (
                            <EyeOff className="h-5 w-5" />
                          ) : (
                            <Eye className="h-5 w-5" />
                          )}
                        </button>
                      </div>
                      
                      {/* Password Requirements */}
                      {formData.password && (
                        <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                          <p className="text-sm font-medium text-gray-700 mb-2">Exigences du mot de passe :</p>
                          <div className="grid grid-cols-1 gap-1">
                            {passwordRequirements.map((req, index) => (
                              <div key={index} className="flex items-center text-sm">
                                {req.test ? (
                                  <Check className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                                ) : (
                                  <X className="h-4 w-4 text-red-500 mr-2 flex-shrink-0" />
                                )}
                                <span className={req.test ? 'text-green-700' : 'text-red-700'}>
                                  {req.text}
                                </span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                    
                    {/* Confirm Password Field */}
                    <div>
                      <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-2">
                        Confirmer le mot de passe
                      </label>
                      <div className="relative group">
                        <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5 group-focus-within:text-purple-500 transition-colors" />
                        <input
                          id="confirmPassword"
                          name="confirmPassword"
                          type={showConfirmPassword ? 'text' : 'password'}
                          required
                          value={formData.confirmPassword}
                          onChange={handleInputChange}
                          className={`w-full pl-11 pr-12 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 hover:border-gray-400 ${
                            formData.confirmPassword && !passwordsMatch 
                              ? 'border-red-300 bg-red-50' 
                              : 'border-gray-300'
                          }`}
                          placeholder="Confirmez votre mot de passe"
                        />
                        <button
                          type="button"
                          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                          onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        >
                          {showConfirmPassword ? (
                            <EyeOff className="h-5 w-5" />
                          ) : (
                            <Eye className="h-5 w-5" />
                          )}
                        </button>
                      </div>
                      
                      {formData.confirmPassword && !passwordsMatch && (
                        <p className="mt-1 text-sm text-red-600 flex items-center">
                          <X className="w-4 h-4 mr-1" />
                          Les mots de passe ne correspondent pas
                        </p>
                      )}

                      {formData.confirmPassword && passwordsMatch && (
                        <p className="mt-1 text-sm text-green-600 flex items-center">
                          <Check className="w-4 h-4 mr-1" />
                          Les mots de passe correspondent
                        </p>
                      )}
                    </div>

                    {/* Terms and Newsletter */}
                    <div className="space-y-4 pt-4 border-t border-gray-200">
                      <label className="flex items-start cursor-pointer">
                        <input
                          id="acceptTerms"
                          name="acceptTerms"
                          type="checkbox"
                          required
                          checked={formData.acceptTerms}
                          onChange={handleInputChange}
                          className="mt-1 h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                        />
                        <span className="ml-3 text-sm text-gray-700">
                          J'accepte les{' '}
                          <Link to="/terms" className="text-purple-600 hover:text-purple-500 font-medium">
                            conditions d'utilisation
                          </Link>{' '}
                          et la{' '}
                          <Link to="/privacy" className="text-purple-600 hover:text-purple-500 font-medium">
                            politique de confidentialité
                          </Link>
                        </span>
                      </label>
                      
                      <label className="flex items-start cursor-pointer">
                        <input
                          id="newsletter"
                          name="newsletter"
                          type="checkbox"
                          checked={formData.newsletter}
                          onChange={handleInputChange}
                          className="mt-1 h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                        />
                        <span className="ml-3 text-sm text-gray-700">
                          Je souhaite recevoir les actualités et offres spéciales de DroneDelivery
                        </span>
                      </label>
                    </div>

                    {/* Navigation Buttons */}
                    <div className="flex space-x-4 pt-4">
                      <Button
                        type="button"
                        onClick={() => setCurrentStep(1)}
                        variant="outline"
                        className="flex-1"
                      >
                        Retour
                      </Button>

                      <Button
                        type="submit"
                        loading={loading}
                        disabled={!isPasswordValid || !passwordsMatch || !formData.acceptTerms}
                        className={`flex-1 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 transform transition-all duration-200 hover:scale-105 ${
                          isAnimating ? 'animate-pulse' : ''
                        }`}
                      >
                        {loading ? 'Création en cours...' : 'Créer mon compte'}
                      </Button>
                    </div>
                  </div>
                )}
              </form>

              {/* Login Link */}
              <div className="mt-8 text-center">
                <p className="text-gray-600">
                  Déjà un compte ?{' '}
                  <Link
                    to="/login"
                    className="font-medium text-purple-600 hover:text-purple-500 transition-colors"
                  >
                    Se connecter
                  </Link>
                </p>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RegisterPage;
