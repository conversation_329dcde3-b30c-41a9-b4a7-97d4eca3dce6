@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

@layer base {
  body {
    font-family: 'Inter', sans-serif;
    @apply antialiased;
  }

  * {
    @apply border-gray-200;
  }

  html {
    @apply scroll-smooth;
  }
}

@layer components {
  /* Buttons */
  .btn-primary {
    @apply bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg focus:ring-4 focus:ring-blue-200;
  }

  .btn-secondary {
    @apply bg-gradient-to-r from-gray-100 to-gray-200 hover:from-gray-200 hover:to-gray-300 text-gray-800 font-semibold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-md focus:ring-4 focus:ring-gray-200;
  }

  .btn-outline {
    @apply border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 focus:ring-4 focus:ring-blue-200;
  }

  /* Cards */
  .card {
    @apply bg-white rounded-2xl shadow-lg hover:shadow-xl p-6 border border-gray-100 transition-all duration-300 transform hover:-translate-y-1;
  }

  .card-hover {
    @apply hover:shadow-2xl hover:-translate-y-2 transition-all duration-500;
  }

  /* Gradients */
  .gradient-primary {
    @apply bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800;
  }

  .gradient-secondary {
    @apply bg-gradient-to-br from-gray-50 to-gray-100;
  }

  .gradient-text {
    @apply bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent;
  }

  /* Animations */
  .animate-fade-in {
    animation: fadeIn 0.6s ease-out;
  }

  .animate-slide-up {
    animation: slideUp 0.8s ease-out;
  }

  .animate-bounce-gentle {
    animation: bounceGentle 2s infinite;
  }

  /* Animations personnalisées */
  .animate-fadeIn {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slideUp {
    animation: slideUp 0.6s ease-out;
  }

  .animate-shake {
    animation: shake 0.5s ease-in-out;
  }

  .animate-bounce-soft {
    animation: bounceSoft 2s infinite;
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  /* Glass effect */
  .glass {
    @apply bg-white/80 backdrop-blur-lg border border-white/20;
  }

  /* Input styles */
  .input-modern {
    @apply w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-4 focus:ring-blue-100 focus:border-blue-500 transition-all duration-300 bg-gray-50 focus:bg-white;
  }

  /* Badge styles */
  .badge {
    @apply inline-flex items-center px-3 py-1 rounded-full text-sm font-medium;
  }

  .badge-primary {
    @apply bg-blue-100 text-blue-800;
  }

  .badge-success {
    @apply bg-green-100 text-green-800;
  }

  .badge-warning {
    @apply bg-yellow-100 text-yellow-800;
  }

  .badge-danger {
    @apply bg-red-100 text-red-800;
  }

  /* Line clamp utility */
  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-3px); }
  20%, 40%, 60%, 80% { transform: translateX(3px); }
}

@keyframes bounceSoft {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-5px); }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

/* Responsive utilities */
@layer utilities {
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }

  .text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0,0,0,0.2);
  }

  .bg-blur {
    backdrop-filter: blur(10px);
  }

  .bg-blur-sm {
    backdrop-filter: blur(4px);
  }
}
