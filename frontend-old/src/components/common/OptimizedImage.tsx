import React, { useState } from 'react';
import { Loader2, ImageIcon, ZoomIn } from 'lucide-react';

interface OptimizedImageProps {
  src: string;
  alt: string;
  className?: string;
  fallbackSrc?: string;
  placeholder?: string;
  lazy?: boolean;
  zoom?: boolean;
  aspectRatio?: 'square' | '4:3' | '16:9' | 'auto';
}

const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  className = "",
  fallbackSrc = "/api/placeholder/400/300",
  placeholder,
  lazy = true,
  zoom = false,
  aspectRatio = 'auto'
}) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);
  const [showZoom, setShowZoom] = useState(false);

  const aspectRatioClasses = {
    'square': 'aspect-square',
    '4:3': 'aspect-[4/3]',
    '16:9': 'aspect-video',
    'auto': ''
  };

  const handleImageLoad = () => {
    setLoading(false);
    setError(false);
  };

  const handleImageError = () => {
    setLoading(false);
    setError(true);
  };

  const containerClasses = `
    relative overflow-hidden bg-gray-100 
    ${aspectRatio !== 'auto' ? aspectRatioClasses[aspectRatio] : ''}
    ${className}
  `;

  return (
    <>
      <div className={containerClasses}>
        {/* Indicateur de chargement */}
        {loading && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
            <div className="text-center space-y-2">
              <Loader2 className="w-8 h-8 text-gray-400 animate-spin mx-auto" />
              {placeholder && (
                <p className="text-sm text-gray-500">{placeholder}</p>
              )}
            </div>
          </div>
        )}

        {/* Image d'erreur */}
        {error && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
            <div className="text-center space-y-2">
              <ImageIcon className="w-12 h-12 text-gray-400 mx-auto" />
              <p className="text-sm text-gray-500">Image non disponible</p>
            </div>
          </div>
        )}

        {/* Image principale */}
        <img
          src={error ? fallbackSrc : src}
          alt={alt}
          loading={lazy ? "lazy" : "eager"}
          onLoad={handleImageLoad}
          onError={handleImageError}
          className={`
            w-full h-full object-cover transition-all duration-300
            ${loading ? 'opacity-0' : 'opacity-100'}
            ${zoom ? 'hover:scale-105 cursor-pointer' : ''}
          `}
          onClick={zoom ? () => setShowZoom(true) : undefined}
        />

        {/* Icône de zoom */}
        {zoom && !loading && !error && (
          <div className="absolute top-2 right-2 p-2 bg-black bg-opacity-50 rounded-full text-white opacity-0 group-hover:opacity-100 transition-opacity">
            <ZoomIn className="w-4 h-4" />
          </div>
        )}
      </div>

      {/* Modal de zoom */}
      {showZoom && (
        <div 
          className="fixed inset-0 z-50 bg-black bg-opacity-90 flex items-center justify-center p-4"
          onClick={() => setShowZoom(false)}
        >
          <div className="relative max-w-full max-h-full">
            <img
              src={src}
              alt={alt}
              className="max-w-full max-h-full object-contain"
            />
            <button
              onClick={() => setShowZoom(false)}
              className="absolute top-4 right-4 p-2 bg-white rounded-full hover:bg-gray-100 transition-colors"
            >
              <ZoomIn className="w-6 h-6 text-gray-700" />
            </button>
          </div>
        </div>
      )}
    </>
  );
};

export default OptimizedImage;
