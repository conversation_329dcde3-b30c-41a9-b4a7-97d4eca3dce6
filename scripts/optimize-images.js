const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

/**
 * Script d'optimisation des images pour DroneDelivery
 * Redimensionne et compresse les images pour le web
 */

const IMAGE_SIZES = {
  thumbnail: { width: 200, height: 200 },
  card: { width: 400, height: 300 },
  detail: { width: 800, height: 600 },
  hero: { width: 1200, height: 800 },
  full: { width: 1920, height: 1080 }
};

const QUALITY_SETTINGS = {
  jpeg: { quality: 85, progressive: true },
  png: { quality: 90, compressionLevel: 9 },
  webp: { quality: 85, effort: 6 }
};

async function optimizeImage(inputPath, outputDir, filename) {
  try {
    const image = sharp(inputPath);
    const metadata = await image.metadata();
    
    console.log(`📸 Optimisation de ${filename}...`);
    
    // Générer différentes tailles
    for (const [sizeName, dimensions] of Object.entries(IMAGE_SIZES)) {
      const outputPath = path.join(outputDir, sizeName);
      
      // Créer le dossier si nécessaire
      if (!fs.existsSync(outputPath)) {
        fs.mkdirSync(outputPath, { recursive: true });
      }
      
      // JPEG optimisé
      await image
        .resize(dimensions.width, dimensions.height, { 
          fit: 'cover', 
          position: 'center' 
        })
        .jpeg(QUALITY_SETTINGS.jpeg)
        .toFile(path.join(outputPath, `${path.parse(filename).name}.jpg`));
      
      // WebP optimisé
      await image
        .resize(dimensions.width, dimensions.height, { 
          fit: 'cover', 
          position: 'center' 
        })
        .webp(QUALITY_SETTINGS.webp)
        .toFile(path.join(outputPath, `${path.parse(filename).name}.webp`));
    }
    
    console.log(`✅ ${filename} optimisé avec succès`);
    
  } catch (error) {
    console.error(`❌ Erreur lors de l'optimisation de ${filename}:`, error);
  }
}

async function processDirectory(inputDir, outputDir) {
  try {
    const files = fs.readdirSync(inputDir);
    
    for (const file of files) {
      const filePath = path.join(inputDir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        // Traitement récursif des sous-dossiers
        await processDirectory(filePath, path.join(outputDir, file));
      } else if (/\.(jpg|jpeg|png|tiff|bmp)$/i.test(file)) {
        // Optimisation des images
        await optimizeImage(filePath, outputDir, file);
      }
    }
  } catch (error) {
    console.error('❌ Erreur lors du traitement du dossier:', error);
  }
}

async function main() {
  const inputDir = process.argv[2] || './src/assets/images';
  const outputDir = process.argv[3] || './public/images/optimized';
  
  console.log('🚀 Démarrage de l\'optimisation des images...');
  console.log(`📁 Dossier source: ${inputDir}`);
  console.log(`📁 Dossier destination: ${outputDir}`);
  
  // Créer le dossier de sortie
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }
  
  await processDirectory(inputDir, outputDir);
  
  console.log('🎉 Optimisation terminée !');
  console.log(`
📊 Résumé:
- Formats générés: JPEG, WebP
- Tailles créées: ${Object.keys(IMAGE_SIZES).join(', ')}
- Dossier de sortie: ${outputDir}

💡 Utilisation dans React:
<picture>
  <source srcSet="/images/optimized/card/image.webp" type="image/webp" />
  <img src="/images/optimized/card/image.jpg" alt="Description" />
</picture>
  `);
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { optimizeImage, processDirectory };
