{"name": "image-optimization-script", "version": "1.0.0", "description": "Script pour optimiser les images de l'application DroneDelivery", "scripts": {"optimize": "node scripts/optimize-images.js", "screenshots": "node scripts/take-screenshots.js", "compress": "node scripts/compress-images.js"}, "devDependencies": {"sharp": "^0.32.0", "playwright": "^1.40.0", "imagemin": "^8.0.1", "imagemin-pngquant": "^9.0.2", "imagemin-mozjpeg": "^10.0.0", "imagemin-webp": "^7.0.0"}}