import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // Optimisations pour le développement
  experimental: {
    // Améliore les performances de Turbopack
    turbo: {
      rules: {
        '*.svg': {
          loaders: ['@svgr/webpack'],
          as: '*.js',
        },
      },
    },
  },

  // Optimisations générales
  swcMinify: true,

  // Configuration pour CopilotKit
  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
      };
    }
    return config;
  },
};

export default nextConfig;
