#!/usr/bin/env python3
"""
LangGraph server compatible with CopilotKit
"""

import os
import uvicorn
from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Dict, Any, List
from dotenv import load_dotenv
from sample_agent.agent import graph, AgentState
import asyncio
import json

# Load environment variables from .env file
load_dotenv()

app = FastAPI(title="LangGraph Agent Server", version="1.0.0")

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:3001"],  # Next.js frontend
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class StreamEvent(BaseModel):
    event: str
    data: Dict[str, Any]

@app.get("/")
async def root():
    return {"message": "LangGraph Agent Server is running!"}

@app.get("/health")
async def health():
    return {"status": "healthy"}

@app.post("/runs/stream")
async def stream_run(request: Request):
    """
    LangGraph API endpoint for streaming runs
    Compatible with CopilotKit langGraphEndpoint
    """
    try:
        body = await request.json()
        
        # Extract input and config from the request
        input_data = body.get("input", {})
        config = body.get("config", {})
        
        # For testing purposes, return a mock response if OpenAI API key is not valid
        openai_key = os.getenv("OPENAI_API_KEY", "")
        
        if not openai_key or openai_key.startswith("sk-or-v1"):
            # Mock response for testing
            messages = input_data.get("messages", [])
            if messages:
                last_message = messages[-1]
                user_content = last_message.get("content", "")
                
                # Check if the message is about changing theme color
                if "theme" in user_content.lower() and "orange" in user_content.lower():
                    mock_response = {
                        "messages": messages + [{
                            "role": "assistant",
                            "content": "I'll set the theme to orange for you!",
                            "tool_calls": [{
                                "id": "call_1",
                                "type": "function", 
                                "function": {
                                    "name": "setThemeColor",
                                    "arguments": json.dumps({"themeColor": "#ff6600"})
                                }
                            }]
                        }]
                    }
                else:
                    mock_response = {
                        "messages": messages + [{
                            "role": "assistant", 
                            "content": f"🤖 Mock Agent: I received '{user_content}'. This is a test response since we need a valid OpenAI API key."
                        }]
                    }
            else:
                mock_response = {
                    "messages": [{
                        "role": "assistant",
                        "content": "Hello! I'm a test agent. Please provide a valid OpenAI API key for full functionality."
                    }]
                }
            
            # Return the response in the expected streaming format
            return {
                "event": "values",
                "data": mock_response
            }
        
        # If we have a valid OpenAI key, use the real agent
        # Initialize state if empty
        if not input_data:
            input_data = {
                "messages": [],
                "proverbs": ["CopilotKit may be new, but its the best thing since sliced bread."],
                "copilotkit": {"actions": []}
            }
        
        # Run the graph asynchronously
        result = await graph.ainvoke(input_data, config)
        
        # Return the result in streaming format
        return {
            "event": "values", 
            "data": result
        }
        
    except Exception as e:
        # Return error in the expected format
        return {
            "event": "error",
            "data": {"error": str(e)}
        }

if __name__ == "__main__":
    port = int(os.getenv("PORT", 8123))
    uvicorn.run(app, host="0.0.0.0", port=port)
