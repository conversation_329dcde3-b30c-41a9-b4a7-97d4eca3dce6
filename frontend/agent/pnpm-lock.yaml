lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

overrides:
  '@langchain/community@<0.3.3': '>=0.3.3'

importers:

  .:
    dependencies:
      '@copilotkit/sdk-js':
        specifier: ^1.5.13
        version: 1.8.3(@browserbasehq/sdk@2.5.0)(@browserbasehq/stagehand@1.14.0(@playwright/test@1.51.1)(deepmerge@4.3.1)(dotenv@16.4.7)(openai@4.90.0(ws@8.18.1)(zod@3.23.8))(zod@3.23.8))(@ibm-cloud/watsonx-ai@1.6.4(@langchain/core@0.3.18(openai@4.90.0(ws@8.18.1)(zod@3.23.8))))(@langchain/anthropic@0.3.8(@langchain/core@0.3.18(openai@4.90.0(ws@8.18.1)(zod@3.23.8))))(@langchain/google-genai@0.1.4(@langchain/core@0.3.18(openai@4.90.0(ws@8.18.1)(zod@3.23.8)))(zod@3.23.8))(axios@1.8.4)(fast-xml-parser@4.5.0)(ibm-cloud-sdk-core@5.3.2)(jsonwebtoken@9.0.2)(openai@4.90.0(ws@8.18.1)(zod@3.23.8))(playwright@1.51.1)(ws@8.18.1)
      '@langchain/anthropic':
        specifier: ^0.3.8
        version: 0.3.8(@langchain/core@0.3.18(openai@4.90.0(ws@8.18.1)(zod@3.23.8)))
      '@langchain/core':
        specifier: ^0.3.18
        version: 0.3.18(openai@4.90.0(ws@8.18.1)(zod@3.23.8))
      '@langchain/google-genai':
        specifier: ^0.1.4
        version: 0.1.4(@langchain/core@0.3.18(openai@4.90.0(ws@8.18.1)(zod@3.23.8)))(zod@3.23.8)
      '@langchain/langgraph':
        specifier: ^0.2.44
        version: 0.2.44(@langchain/core@0.3.18(openai@4.90.0(ws@8.18.1)(zod@3.23.8)))
      '@langchain/openai':
        specifier: ^0.3.14
        version: 0.3.14(@langchain/core@0.3.18(openai@4.90.0(ws@8.18.1)(zod@3.23.8)))
      zod:
        specifier: ^3.23.8
        version: 3.23.8
    devDependencies:
      '@types/html-to-text':
        specifier: ^9.0.4
        version: 9.0.4
      '@types/node':
        specifier: ^22.9.0
        version: 22.9.1
      typescript:
        specifier: ^5.6.3
        version: 5.6.3

packages:

  '@anthropic-ai/sdk@0.27.3':
    resolution: {integrity: sha512-IjLt0gd3L4jlOfilxVXTifn42FnVffMgDC04RJK1KDZpmkBWLv0XC92MVVmkxrFZNS/7l3xWgP/I3nqtX1sQHw==}

  '@browserbasehq/sdk@2.5.0':
    resolution: {integrity: sha512-bcnbYZvm5Ht1nrHUfWDK4crspiTy1ESJYMApsMiOTUnlKOan0ocRD6m7hZH34iSC2c2XWsoryR80cwsYgCBWzQ==}

  '@browserbasehq/stagehand@1.14.0':
    resolution: {integrity: sha512-Hi/EzgMFWz+FKyepxHTrqfTPjpsuBS4zRy3e9sbMpBgLPv+9c0R+YZEvS7Bw4mTS66QtvvURRT6zgDGFotthVQ==}
    peerDependencies:
      '@playwright/test': ^1.42.1
      deepmerge: ^4.3.1
      dotenv: ^16.4.5
      openai: ^4.62.1
      zod: ^3.23.8

  '@copilotkit/sdk-js@1.8.3':
    resolution: {integrity: sha512-Ck8FZX6qx7ZSdW08QG1ZWyirU3kWBh0x/byD9BIFvSAGHOK95+rCPzv+tMCIRoxZVYt0Hf2jdaaaFjUsb73wOQ==}

  '@copilotkit/shared@1.8.3':
    resolution: {integrity: sha512-xNmmMZjygehB+5iZbheldIitnEt+1bA25E7hNdnobSb9ScSONEu0eJPb6yhWu0j588JfBgCGZOJ5A2SWwr/6Jw==}

  '@google/generative-ai@0.21.0':
    resolution: {integrity: sha512-7XhUbtnlkSEZK15kN3t+tzIMxsbKm/dSkKBFalj+20NvPKe1kBY7mR2P7vuijEn+f06z5+A8bVGKO0v39cr6Wg==}
    engines: {node: '>=18.0.0'}

  '@ibm-cloud/watsonx-ai@1.6.4':
    resolution: {integrity: sha512-u0fmXagywjLAd3apZTV6kRQAHjWl3bNKv5422mQJsM/MZB5YPjx28tjEbpoORh15RuWjYyO/wHZACRWBBkNhbQ==}
    engines: {node: '>=18.0.0'}

  '@langchain/anthropic@0.3.8':
    resolution: {integrity: sha512-7qeRDhNnCf1peAbjY825R2HNszobJeGvqi2cfPl+YsduDIYEGUzfoGRRarPI5joIGX5YshCsch6NFtap2bLfmw==}
    engines: {node: '>=18'}
    peerDependencies:
      '@langchain/core': '>=0.2.21 <0.4.0'

  '@langchain/community@0.3.40':
    resolution: {integrity: sha512-UvpEebdFKJsjFBKeUOvvYHOEFsUcjZnyU1qNirtDajwjzTJlszXtv+Mq8F6w5mJsznpI9x7ZMNzAqydVxMG5hA==}
    engines: {node: '>=18'}
    peerDependencies:
      '@arcjet/redact': ^v1.0.0-alpha.23
      '@aws-crypto/sha256-js': ^5.0.0
      '@aws-sdk/client-bedrock-agent-runtime': ^3.749.0
      '@aws-sdk/client-bedrock-runtime': ^3.749.0
      '@aws-sdk/client-dynamodb': ^3.749.0
      '@aws-sdk/client-kendra': ^3.749.0
      '@aws-sdk/client-lambda': ^3.749.0
      '@aws-sdk/client-s3': ^3.749.0
      '@aws-sdk/client-sagemaker-runtime': ^3.749.0
      '@aws-sdk/client-sfn': ^3.749.0
      '@aws-sdk/credential-provider-node': ^3.388.0
      '@aws-sdk/dsql-signer': '*'
      '@azure/search-documents': ^12.0.0
      '@azure/storage-blob': ^12.15.0
      '@browserbasehq/sdk': '*'
      '@browserbasehq/stagehand': ^1.0.0
      '@clickhouse/client': ^0.2.5
      '@cloudflare/ai': '*'
      '@datastax/astra-db-ts': ^1.0.0
      '@elastic/elasticsearch': ^8.4.0
      '@getmetal/metal-sdk': '*'
      '@getzep/zep-cloud': ^1.0.6
      '@getzep/zep-js': ^0.9.0
      '@gomomento/sdk': ^1.51.1
      '@gomomento/sdk-core': ^1.51.1
      '@google-ai/generativelanguage': '*'
      '@google-cloud/storage': ^6.10.1 || ^7.7.0
      '@gradientai/nodejs-sdk': ^1.2.0
      '@huggingface/inference': ^2.6.4
      '@huggingface/transformers': ^3.2.3
      '@ibm-cloud/watsonx-ai': '*'
      '@lancedb/lancedb': ^0.12.0
      '@langchain/core': '>=0.2.21 <0.4.0'
      '@layerup/layerup-security': ^1.5.12
      '@libsql/client': ^0.14.0
      '@mendable/firecrawl-js': ^1.4.3
      '@mlc-ai/web-llm': '*'
      '@mozilla/readability': '*'
      '@neondatabase/serverless': '*'
      '@notionhq/client': ^2.2.10
      '@opensearch-project/opensearch': '*'
      '@pinecone-database/pinecone': '*'
      '@planetscale/database': ^1.8.0
      '@premai/prem-sdk': ^0.3.25
      '@qdrant/js-client-rest': ^1.8.2
      '@raycast/api': ^1.55.2
      '@rockset/client': ^0.9.1
      '@smithy/eventstream-codec': ^2.0.5
      '@smithy/protocol-http': ^3.0.6
      '@smithy/signature-v4': ^2.0.10
      '@smithy/util-utf8': ^2.0.0
      '@spider-cloud/spider-client': ^0.0.21
      '@supabase/supabase-js': ^2.45.0
      '@tensorflow-models/universal-sentence-encoder': '*'
      '@tensorflow/tfjs-converter': '*'
      '@tensorflow/tfjs-core': '*'
      '@upstash/ratelimit': ^1.1.3 || ^2.0.3
      '@upstash/redis': ^1.20.6
      '@upstash/vector': ^1.1.1
      '@vercel/kv': '*'
      '@vercel/postgres': '*'
      '@writerai/writer-sdk': ^0.40.2
      '@xata.io/client': ^0.28.0
      '@zilliz/milvus2-sdk-node': '>=2.3.5'
      apify-client: ^2.7.1
      assemblyai: ^4.6.0
      azion: ^1.11.1
      better-sqlite3: '>=9.4.0 <12.0.0'
      cassandra-driver: ^4.7.2
      cborg: ^4.1.1
      cheerio: ^1.0.0-rc.12
      chromadb: '*'
      closevector-common: 0.1.3
      closevector-node: 0.1.6
      closevector-web: 0.1.6
      cohere-ai: '*'
      convex: ^1.3.1
      crypto-js: ^4.2.0
      d3-dsv: ^2.0.0
      discord.js: ^14.14.1
      dria: ^0.0.3
      duck-duck-scrape: ^2.2.5
      epub2: ^3.0.1
      fast-xml-parser: '*'
      firebase-admin: ^11.9.0 || ^12.0.0
      google-auth-library: '*'
      googleapis: '*'
      hnswlib-node: ^3.0.0
      html-to-text: ^9.0.5
      ibm-cloud-sdk-core: '*'
      ignore: ^5.2.0
      interface-datastore: ^8.2.11
      ioredis: ^5.3.2
      it-all: ^3.0.4
      jsdom: '*'
      jsonwebtoken: ^9.0.2
      llmonitor: ^0.5.9
      lodash: ^4.17.21
      lunary: ^0.7.10
      mammoth: ^1.6.0
      mariadb: ^3.4.0
      mem0ai: ^2.1.8
      mongodb: '>=5.2.0'
      mysql2: ^3.9.8
      neo4j-driver: '*'
      notion-to-md: ^3.1.0
      officeparser: ^4.0.4
      openai: '*'
      pdf-parse: 1.1.1
      pg: ^8.11.0
      pg-copy-streams: ^6.0.5
      pickleparser: ^0.2.1
      playwright: ^1.32.1
      portkey-ai: ^0.1.11
      puppeteer: '*'
      pyodide: '>=0.24.1 <0.27.0'
      redis: '*'
      replicate: '*'
      sonix-speech-recognition: ^2.1.1
      srt-parser-2: ^1.2.3
      typeorm: ^0.3.20
      typesense: ^1.5.3
      usearch: ^1.1.1
      voy-search: 0.6.2
      weaviate-ts-client: '*'
      web-auth-library: ^1.0.3
      word-extractor: '*'
      ws: ^8.14.2
      youtubei.js: '*'
    peerDependenciesMeta:
      '@arcjet/redact':
        optional: true
      '@aws-crypto/sha256-js':
        optional: true
      '@aws-sdk/client-bedrock-agent-runtime':
        optional: true
      '@aws-sdk/client-bedrock-runtime':
        optional: true
      '@aws-sdk/client-dynamodb':
        optional: true
      '@aws-sdk/client-kendra':
        optional: true
      '@aws-sdk/client-lambda':
        optional: true
      '@aws-sdk/client-s3':
        optional: true
      '@aws-sdk/client-sagemaker-runtime':
        optional: true
      '@aws-sdk/client-sfn':
        optional: true
      '@aws-sdk/credential-provider-node':
        optional: true
      '@aws-sdk/dsql-signer':
        optional: true
      '@azure/search-documents':
        optional: true
      '@azure/storage-blob':
        optional: true
      '@browserbasehq/sdk':
        optional: true
      '@clickhouse/client':
        optional: true
      '@cloudflare/ai':
        optional: true
      '@datastax/astra-db-ts':
        optional: true
      '@elastic/elasticsearch':
        optional: true
      '@getmetal/metal-sdk':
        optional: true
      '@getzep/zep-cloud':
        optional: true
      '@getzep/zep-js':
        optional: true
      '@gomomento/sdk':
        optional: true
      '@gomomento/sdk-core':
        optional: true
      '@google-ai/generativelanguage':
        optional: true
      '@google-cloud/storage':
        optional: true
      '@gradientai/nodejs-sdk':
        optional: true
      '@huggingface/inference':
        optional: true
      '@huggingface/transformers':
        optional: true
      '@lancedb/lancedb':
        optional: true
      '@layerup/layerup-security':
        optional: true
      '@libsql/client':
        optional: true
      '@mendable/firecrawl-js':
        optional: true
      '@mlc-ai/web-llm':
        optional: true
      '@mozilla/readability':
        optional: true
      '@neondatabase/serverless':
        optional: true
      '@notionhq/client':
        optional: true
      '@opensearch-project/opensearch':
        optional: true
      '@pinecone-database/pinecone':
        optional: true
      '@planetscale/database':
        optional: true
      '@premai/prem-sdk':
        optional: true
      '@qdrant/js-client-rest':
        optional: true
      '@raycast/api':
        optional: true
      '@rockset/client':
        optional: true
      '@smithy/eventstream-codec':
        optional: true
      '@smithy/protocol-http':
        optional: true
      '@smithy/signature-v4':
        optional: true
      '@smithy/util-utf8':
        optional: true
      '@spider-cloud/spider-client':
        optional: true
      '@supabase/supabase-js':
        optional: true
      '@tensorflow-models/universal-sentence-encoder':
        optional: true
      '@tensorflow/tfjs-converter':
        optional: true
      '@tensorflow/tfjs-core':
        optional: true
      '@upstash/ratelimit':
        optional: true
      '@upstash/redis':
        optional: true
      '@upstash/vector':
        optional: true
      '@vercel/kv':
        optional: true
      '@vercel/postgres':
        optional: true
      '@writerai/writer-sdk':
        optional: true
      '@xata.io/client':
        optional: true
      '@zilliz/milvus2-sdk-node':
        optional: true
      apify-client:
        optional: true
      assemblyai:
        optional: true
      azion:
        optional: true
      better-sqlite3:
        optional: true
      cassandra-driver:
        optional: true
      cborg:
        optional: true
      cheerio:
        optional: true
      chromadb:
        optional: true
      closevector-common:
        optional: true
      closevector-node:
        optional: true
      closevector-web:
        optional: true
      cohere-ai:
        optional: true
      convex:
        optional: true
      crypto-js:
        optional: true
      d3-dsv:
        optional: true
      discord.js:
        optional: true
      dria:
        optional: true
      duck-duck-scrape:
        optional: true
      epub2:
        optional: true
      fast-xml-parser:
        optional: true
      firebase-admin:
        optional: true
      google-auth-library:
        optional: true
      googleapis:
        optional: true
      hnswlib-node:
        optional: true
      html-to-text:
        optional: true
      ignore:
        optional: true
      interface-datastore:
        optional: true
      ioredis:
        optional: true
      it-all:
        optional: true
      jsdom:
        optional: true
      jsonwebtoken:
        optional: true
      llmonitor:
        optional: true
      lodash:
        optional: true
      lunary:
        optional: true
      mammoth:
        optional: true
      mariadb:
        optional: true
      mem0ai:
        optional: true
      mongodb:
        optional: true
      mysql2:
        optional: true
      neo4j-driver:
        optional: true
      notion-to-md:
        optional: true
      officeparser:
        optional: true
      pdf-parse:
        optional: true
      pg:
        optional: true
      pg-copy-streams:
        optional: true
      pickleparser:
        optional: true
      playwright:
        optional: true
      portkey-ai:
        optional: true
      puppeteer:
        optional: true
      pyodide:
        optional: true
      redis:
        optional: true
      replicate:
        optional: true
      sonix-speech-recognition:
        optional: true
      srt-parser-2:
        optional: true
      typeorm:
        optional: true
      typesense:
        optional: true
      usearch:
        optional: true
      voy-search:
        optional: true
      weaviate-ts-client:
        optional: true
      web-auth-library:
        optional: true
      word-extractor:
        optional: true
      ws:
        optional: true
      youtubei.js:
        optional: true

  '@langchain/core@0.3.18':
    resolution: {integrity: sha512-IEZCrFs1Xd0J2FTH1D3Lnm3/Yk2r8LSpwDeLYwcCom3rNAK5k4mKQ2rwIpNq3YuqBdrTNMKRO+PopjkP1SB17A==}
    engines: {node: '>=18'}

  '@langchain/google-genai@0.1.4':
    resolution: {integrity: sha512-b8qrqnHYbNseaAikrWyxuDTww6CUIse82F5/BmF2GtWVR25yJrNUWETfTp7o7iIMxhFR0PuQag4gEZOL74F5Tw==}
    engines: {node: '>=18'}
    peerDependencies:
      '@langchain/core': '>=0.3.17 <0.4.0'

  '@langchain/langgraph-checkpoint@0.0.15':
    resolution: {integrity: sha512-AiJkvsYHqNbCh1Tx823qs2lf2qRqeB4EAMejirOk8gkpPszAGYua5c3niKYkcKR2tU8Snhrmj7Gm9HKZSFOXyw==}
    engines: {node: '>=18'}
    peerDependencies:
      '@langchain/core': '>=0.2.31 <0.4.0'

  '@langchain/langgraph-sdk@0.0.36':
    resolution: {integrity: sha512-KkAZM0uXBaMcD/dpGTBppOhbvNX6gz+Y1zFAC898OblegFkSvICrkd0oRQ5Ro/GWK/NAoDymnMUDXeZDdUkSuw==}

  '@langchain/langgraph@0.2.44':
    resolution: {integrity: sha512-CR9LB7sytdx0Ink56qVUPorDo5gW5m7iOU2ypu1OYA4l5aIrT4xGvHCwrGH9RE80pb/d0FglVUkEgEfuvSDbmw==}
    engines: {node: '>=18'}
    peerDependencies:
      '@langchain/core': '>=0.2.36 <0.3.0 || >=0.3.9 < 0.4.0'

  '@langchain/openai@0.3.14':
    resolution: {integrity: sha512-lNWjUo1tbvsss45IF7UQtMu1NJ6oUKvhgPYWXnX9f/d6OmuLu7D99HQ3Y88vLcUo9XjjOy417olYHignMduMjA==}
    engines: {node: '>=18'}
    peerDependencies:
      '@langchain/core': '>=0.2.26 <0.4.0'

  '@langchain/textsplitters@0.1.0':
    resolution: {integrity: sha512-djI4uw9rlkAb5iMhtLED+xJebDdAG935AdP4eRTB02R7OB/act55Bj9wsskhZsvuyQRpO4O1wQOp85s6T6GWmw==}
    engines: {node: '>=18'}
    peerDependencies:
      '@langchain/core': '>=0.2.21 <0.4.0'

  '@lukeed/csprng@1.1.0':
    resolution: {integrity: sha512-Z7C/xXCiGWsg0KuKsHTKJxbWhpI3Vs5GwLfOean7MGyVFGqdRgBbAjOCh6u4bbjPc/8MJ2pZmK/0DLdCbivLDA==}
    engines: {node: '>=8'}

  '@lukeed/uuid@2.0.1':
    resolution: {integrity: sha512-qC72D4+CDdjGqJvkFMMEAtancHUQ7/d/tAiHf64z8MopFDmcrtbcJuerDtFceuAfQJ2pDSfCKCtbqoGBNnwg0w==}
    engines: {node: '>=8'}

  '@playwright/test@1.51.1':
    resolution: {integrity: sha512-nM+kEaTSAoVlXmMPH10017vn3FSiFqr/bh4fKg9vmAdMfd9SDqRZNvPSiAHADc/itWak+qPvMPZQOPwCBW7k7Q==}
    engines: {node: '>=18'}
    hasBin: true

  '@segment/analytics-core@1.8.1':
    resolution: {integrity: sha512-EYcdBdhfi1pOYRX+Sf5orpzzYYFmDHTEu6+w0hjXpW5bWkWct+Nv6UJg1hF4sGDKEQjpZIinLTpQ4eioFM4KeQ==}

  '@segment/analytics-generic-utils@1.2.0':
    resolution: {integrity: sha512-DfnW6mW3YQOLlDQQdR89k4EqfHb0g/3XvBXkovH1FstUN93eL1kfW9CsDcVQyH3bAC5ZsFyjA/o/1Q2j0QeoWw==}

  '@segment/analytics-node@2.2.1':
    resolution: {integrity: sha512-J+p5r2BewzowI6YsnSH6U+W9IAvRbEyheqDOSUEwh6QDbxjwcBXwzuPwtz5DtEjbEGMu2QwrwoJvZlZ/n5fugw==}
    engines: {node: '>=18'}

  '@tokenizer/token@0.3.0':
    resolution: {integrity: sha512-OvjF+z51L3ov0OyAU0duzsYuvO01PH7x4t6DJx+guahgTnBHkhJdG7soQeTSFLWN3efnHyibZ4Z8l2EuWwJN3A==}

  '@types/debug@4.1.12':
    resolution: {integrity: sha512-vIChWdVG3LG1SMxEvI/AK+FWJthlrqlTu7fbrlywTkkaONwk/UAGaULXRlf8vkzFBLVm0zkMdCquhL5aOjhXPQ==}

  '@types/html-to-text@9.0.4':
    resolution: {integrity: sha512-pUY3cKH/Nm2yYrEmDlPR1mR7yszjGx4DrwPjQ702C4/D5CwHuZTgZdIdwPkRbcuhs7BAh2L5rg3CL5cbRiGTCQ==}

  '@types/json-schema@7.0.15':
    resolution: {integrity: sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==}

  '@types/ms@2.1.0':
    resolution: {integrity: sha512-GsCCIZDE/p3i96vtEqx+7dBUGXrc7zeSK3wwPHIaRThS+9OhWIXRqzs4d6k1SVU8g91DrNRWxWUGhp5KXQb2VA==}

  '@types/node-fetch@2.6.12':
    resolution: {integrity: sha512-8nneRWKCg3rMtF69nLQJnOYUcbafYeFSjqkw3jCRLsqkWFlHaoQrr5mXmofFGOx3DKn7UfmBMyov8ySvLRVldA==}

  '@types/node@18.19.64':
    resolution: {integrity: sha512-955mDqvO2vFf/oL7V3WiUtiz+BugyX8uVbaT2H8oj3+8dRyH2FLiNdowe7eNqRM7IOIZvzDH76EoAT+gwm6aIQ==}

  '@types/node@18.19.84':
    resolution: {integrity: sha512-ACYy2HGcZPHxEeWTqowTF7dhXN+JU1o7Gr4b41klnn6pj2LD6rsiGqSZojMdk1Jh2ys3m76ap+ae1vvE4+5+vg==}

  '@types/node@22.9.1':
    resolution: {integrity: sha512-p8Yy/8sw1caA8CdRIQBG5tiLHmxtQKObCijiAa9Ez+d4+PRffM4054xbju0msf+cvhJpnFEeNjxmVT/0ipktrg==}

  '@types/retry@0.12.0':
    resolution: {integrity: sha512-wWKOClTTiizcZhXnPY4wikVAwmdYHp8q6DmC+EJUzAMsycb7HB32Kh9RN4+0gExjmPmZSAQjgURXIGATPegAvA==}

  '@types/tough-cookie@4.0.5':
    resolution: {integrity: sha512-/Ad8+nIOV7Rl++6f1BdKxFSMgmoqEoYbHRpPcx3JEfv8VRsQe9Z4mCXeJBzxs7mbHY/XOZZuXlRNfhpVPbs6ZA==}

  '@types/uuid@10.0.0':
    resolution: {integrity: sha512-7gqG38EyHgyP1S+7+xomFtL+ZNHcKv6DwNaCZmJmo1vgMugyF3TCnXVg4t1uk89mLNwnLtnY3TpOpCOyp1/xHQ==}

  abort-controller@3.0.0:
    resolution: {integrity: sha512-h8lQ8tacZYnR3vNQTgibj+tODHI5/+l06Au2Pcriv/Gmet0eaj4TwWH41sO9wnHDiQsEj19q0drzdWdeAHtweg==}
    engines: {node: '>=6.5'}

  agentkeepalive@4.5.0:
    resolution: {integrity: sha512-5GG/5IbQQpC9FpkRGsSvZI5QYeSCzlJHdpBQntCsuTOxhKD8lqKhrleg2Yi7yvMIf82Ycmmqln9U8V9qwEiJew==}
    engines: {node: '>= 8.0.0'}

  agentkeepalive@4.6.0:
    resolution: {integrity: sha512-kja8j7PjmncONqaTsB8fQ+wE2mSU2DJ9D4XKoJ5PFWIdRMa6SLSN1ff4mOr4jCbfRSsxR4keIiySJU0N9T5hIQ==}
    engines: {node: '>= 8.0.0'}

  ansi-styles@4.3.0:
    resolution: {integrity: sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==}
    engines: {node: '>=8'}

  ansi-styles@5.2.0:
    resolution: {integrity: sha512-Cxwpt2SfTzTtXcfOlzGEee8O+c+MmUgGrNiBcXnuWxuFJHe6a5Hz7qwhwe5OgaSYI0IJvkLqWX1ASG+cJOkEiA==}
    engines: {node: '>=10'}

  argparse@2.0.1:
    resolution: {integrity: sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==}

  asynckit@0.4.0:
    resolution: {integrity: sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==}

  axios@1.8.4:
    resolution: {integrity: sha512-eBSYY4Y68NNlHbHBMdeDmKNtDgXWhQsJcGqzO3iLUM0GraQFSS9cVgPX5I9b3lbdFKyYoAEGAZF1DwhTaljNAw==}

  base64-js@1.5.1:
    resolution: {integrity: sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==}

  binary-extensions@2.3.0:
    resolution: {integrity: sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==}
    engines: {node: '>=8'}

  buffer-equal-constant-time@1.0.1:
    resolution: {integrity: sha512-zRpUiDwd/xk6ADqPMATG8vc9VPrkck7T07OIx0gnjmJAnHnTVXNQG3vfvWNuiZIkwu9KrKdA1iJKfsfTVxE6NA==}

  buffer@6.0.3:
    resolution: {integrity: sha512-FTiCpNxtwiZZHEZbcbTIcZjERVICn9yq/pDFkTl95/AxzD1naBctN7YO68riM/gLSDY7sdrMby8hofADYuuqOA==}

  camelcase@6.3.0:
    resolution: {integrity: sha512-Gmy6FhYlCY7uOElZUSbxo2UCDH8owEk996gkbrpsgGtrJLM3J7jGxl9Ic7Qwwj4ivOE5AWZWRMecDdF7hqGjFA==}
    engines: {node: '>=10'}

  chalk@4.1.2:
    resolution: {integrity: sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==}
    engines: {node: '>=10'}

  color-convert@2.0.1:
    resolution: {integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==}
    engines: {node: '>=7.0.0'}

  color-name@1.1.4:
    resolution: {integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==}

  combined-stream@1.0.8:
    resolution: {integrity: sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==}
    engines: {node: '>= 0.8'}

  commander@10.0.1:
    resolution: {integrity: sha512-y4Mg2tXshplEbSGzx7amzPwKKOCGuoSRP/CjEdwwk0FOGlUbq6lKuoyDZTNZkmxHdJtp54hdfY/JUrdL7Xfdug==}
    engines: {node: '>=14'}

  console-table-printer@2.12.1:
    resolution: {integrity: sha512-wKGOQRRvdnd89pCeH96e2Fn4wkbenSP6LMHfjfyNLMbGuHEFbMqQNuxXqd0oXG9caIOQ1FTvc5Uijp9/4jujnQ==}

  debug@4.4.0:
    resolution: {integrity: sha512-6WTZ/IxCY/T6BALoZHaE4ctp9xm+Z5kY/pzYaCHRFeyVhojxlrm+46y68HA6hr0TcwEssoxNiDEUJQjfPZ/RYA==}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  decamelize@1.2.0:
    resolution: {integrity: sha512-z2S+W9X73hAUUki+N+9Za2lBlun89zigOyGrsax+KUQ6wKW4ZoWpEYBkGhQjwAjjDCkWxhY0VKEhk8wzY7F5cA==}
    engines: {node: '>=0.10.0'}

  deepmerge@4.3.1:
    resolution: {integrity: sha512-3sUqbMEc77XqpdNO7FRyRog+eW3ph+GYCbj+rK+uYyRMuwsVy0rMiVtPn+QJlKFvWP/1PYpapqYn0Me2knFn+A==}
    engines: {node: '>=0.10.0'}

  delayed-stream@1.0.0:
    resolution: {integrity: sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==}
    engines: {node: '>=0.4.0'}

  dotenv@16.4.7:
    resolution: {integrity: sha512-47qPchRCykZC03FhkYAhrvwU4xDBFIj1QPqaarj6mdM/hgUzfPHcpkHJOn3mJAufFeeAxAzeGsr5X0M4k6fLZQ==}
    engines: {node: '>=12'}

  dset@3.1.4:
    resolution: {integrity: sha512-2QF/g9/zTaPDc3BjNcVTGoBbXBgYfMTTceLaYcFJ/W9kggFUkhxD/hMEeuLKbugyef9SqAx8cpgwlIP/jinUTA==}
    engines: {node: '>=4'}

  ecdsa-sig-formatter@1.0.11:
    resolution: {integrity: sha512-nagl3RYrbNv6kQkeJIpt6NJZy8twLB/2vtz6yN9Z4vRKHN4/QZJIEbqohALSgwKdnksuY3k5Addp5lg8sVoVcQ==}

  event-target-shim@5.0.1:
    resolution: {integrity: sha512-i/2XbnSz/uxRCU6+NdVJgKWDTM427+MqYbkQzD321DuCQJUqOuJKIA0IM2+W2xtYHdKOmZ4dR6fExsd4SXL+WQ==}
    engines: {node: '>=6'}

  eventemitter3@4.0.7:
    resolution: {integrity: sha512-8guHBZCwKnFhYdHr2ysuRWErTwhoN2X8XELRlrRwpmfeY2jjuUN4taQMsULKUVo1K4DvZl+0pgfyoysHxvmvEw==}

  events@3.3.0:
    resolution: {integrity: sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q==}
    engines: {node: '>=0.8.x'}

  expr-eval@2.0.2:
    resolution: {integrity: sha512-4EMSHGOPSwAfBiibw3ndnP0AvjDWLsMvGOvWEZ2F96IGk0bIVdjQisOHxReSkE13mHcfbuCiXw+G4y0zv6N8Eg==}

  extend@3.0.2:
    resolution: {integrity: sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g==}

  fast-xml-parser@4.5.0:
    resolution: {integrity: sha512-/PlTQCI96+fZMAOLMZK4CWG1ItCbfZ/0jx7UIJFChPNrx7tcEgerUgWbeieCM9MfHInUDyK8DWYZ+YrywDJuTg==}
    hasBin: true

  file-type@16.5.4:
    resolution: {integrity: sha512-/yFHK0aGjFEgDJjEKP0pWCplsPFPhwyfwevf/pVxiN0tmE4L9LmwWxWukdJSHdoCli4VgQLehjJtwQBnqmsKcw==}
    engines: {node: '>=10'}

  flat@5.0.2:
    resolution: {integrity: sha512-b6suED+5/3rTpUBdG1gupIl8MPFCAMA0QXwmljLhvCUKcUvdE4gWky9zpuGCcXHOsz4J9wPGNWq6OKpmIzz3hQ==}
    hasBin: true

  follow-redirects@1.15.9:
    resolution: {integrity: sha512-gew4GsXizNgdoRyqmyfMHyAmXsZDk6mHkSxZFCzW9gwlbtOW44CDtYavM+y+72qD/Vq2l550kMF52DT8fOLJqQ==}
    engines: {node: '>=4.0'}
    peerDependencies:
      debug: '*'
    peerDependenciesMeta:
      debug:
        optional: true

  form-data-encoder@1.7.2:
    resolution: {integrity: sha512-qfqtYan3rxrnCk1VYaA4H+Ms9xdpPqvLZa6xmMgFvhO32x7/3J/ExcTd6qpxM0vH2GdMI+poehyBZvqfMTto8A==}

  form-data@4.0.0:
    resolution: {integrity: sha512-ETEklSGi5t0QMZuiXoA/Q6vcnxcLQP5vdugSpuAyi6SVGi2clPPp+xgEhuMaHC+zGgn31Kd235W35f7Hykkaww==}
    engines: {node: '>= 6'}

  form-data@4.0.1:
    resolution: {integrity: sha512-tzN8e4TX8+kkxGPK8D5u0FNmjPUjw3lwC9lSLxxoB/+GtsJG91CO8bSWy73APlgAZzZbXEYZJuxjkHH2w+Ezhw==}
    engines: {node: '>= 6'}

  formdata-node@4.4.1:
    resolution: {integrity: sha512-0iirZp3uVDjVGt9p49aTaqjk84TrglENEDuqfdlZQ1roC9CWlPk6Avf8EEnZNcAqPonwkG35x4n3ww/1THYAeQ==}
    engines: {node: '>= 12.20'}

  fsevents@2.3.2:
    resolution: {integrity: sha512-xiqMQR4xAeHTuB9uWm+fFRcIOgKBMiOBP+eXiyT7jsgVCq1bkVygt00oASowB7EdtpOHaaPgKt812P9ab+DDKA==}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]

  graphql@16.10.0:
    resolution: {integrity: sha512-AjqGKbDGUFRKIRCP9tCKiIGHyriz2oHEbPIbEtcSLSs4YjReZOIPQQWek4+6hjw62H9QShXHyaGivGiYVLeYFQ==}
    engines: {node: ^12.22.0 || ^14.16.0 || ^16.0.0 || >=17.0.0}

  has-flag@4.0.0:
    resolution: {integrity: sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==}
    engines: {node: '>=8'}

  humanize-ms@1.2.1:
    resolution: {integrity: sha512-Fl70vYtsAFb/C06PTS9dZBo7ihau+Tu/DNCk/OyHhea07S+aeMWpFFkUaXRa8fI+ScZbEI8dfSxwY7gxZ9SAVQ==}

  ibm-cloud-sdk-core@5.3.2:
    resolution: {integrity: sha512-YhtS+7hGNO61h/4jNShHxbbuJ1TnDqiFKQzfEaqePnonOvv8NnxWxOk92FlKKCCzZNOT34Gnd7WCLVJTntwEFQ==}
    engines: {node: '>=18'}

  ieee754@1.2.1:
    resolution: {integrity: sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==}

  isstream@0.1.2:
    resolution: {integrity: sha512-Yljz7ffyPbrLpLngrMtZ7NduUgVvi6wG9RJ9IUcyCd59YQ911PBJphODUcbOVbqYfxe1wuYf/LJ8PauMRwsM/g==}

  jose@5.10.0:
    resolution: {integrity: sha512-s+3Al/p9g32Iq+oqXxkW//7jk2Vig6FF1CFqzVXoTUXt2qz89YWbL+OwS17NFYEvxC35n0FKeGO2LGYSxeM2Gg==}

  js-tiktoken@1.0.15:
    resolution: {integrity: sha512-65ruOWWXDEZHHbAo7EjOcNxOGasQKbL4Fq3jEr2xsCqSsoOo6VVSqzWQb6PRIqypFSDcma4jO90YP0w5X8qVXQ==}

  js-tiktoken@1.0.19:
    resolution: {integrity: sha512-XC63YQeEcS47Y53gg950xiZ4IWmkfMe4p2V9OSaBt26q+p47WHn18izuXzSclCI73B7yGqtfRsT6jcZQI0y08g==}

  js-yaml@4.1.0:
    resolution: {integrity: sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==}
    hasBin: true

  jsonpointer@5.0.1:
    resolution: {integrity: sha512-p/nXbhSEcu3pZRdkW1OfJhpsVtW1gd4Wa1fnQc9YLiTfAjn0312eMKimbdIQzuZl9aa9xUGaRlP9T/CJE/ditQ==}
    engines: {node: '>=0.10.0'}

  jsonwebtoken@9.0.2:
    resolution: {integrity: sha512-PRp66vJ865SSqOlgqS8hujT5U4AOgMfhrwYIuIhfKaoSCZcirrmASQr8CX7cUg+RMih+hgznrjp99o+W4pJLHQ==}
    engines: {node: '>=12', npm: '>=6'}

  jwa@1.4.1:
    resolution: {integrity: sha512-qiLX/xhEEFKUAJ6FiBMbes3w9ATzyk5W7Hvzpa/SLYdxNtng+gcurvrI7TbACjIXlsJyr05/S1oUhZrc63evQA==}

  jws@3.2.2:
    resolution: {integrity: sha512-YHlZCB6lMTllWDtSPHz/ZXTsi8S00usEV6v1tjq8tOUZzw7DpSDWVXjXDre6ed1w/pd495ODpHZYSdkRTsa0HA==}

  langchain@0.3.19:
    resolution: {integrity: sha512-aGhoTvTBS5ulatA67RHbJ4bcV5zcYRYdm5IH+hpX99RYSFXG24XF3ghSjhYi6sxW+SUnEQ99fJhA5kroVpKNhw==}
    engines: {node: '>=18'}
    peerDependencies:
      '@langchain/anthropic': '*'
      '@langchain/aws': '*'
      '@langchain/cerebras': '*'
      '@langchain/cohere': '*'
      '@langchain/core': '>=0.2.21 <0.4.0'
      '@langchain/deepseek': '*'
      '@langchain/google-genai': '*'
      '@langchain/google-vertexai': '*'
      '@langchain/google-vertexai-web': '*'
      '@langchain/groq': '*'
      '@langchain/mistralai': '*'
      '@langchain/ollama': '*'
      '@langchain/xai': '*'
      axios: '*'
      cheerio: '*'
      handlebars: ^4.7.8
      peggy: ^3.0.2
      typeorm: '*'
    peerDependenciesMeta:
      '@langchain/anthropic':
        optional: true
      '@langchain/aws':
        optional: true
      '@langchain/cerebras':
        optional: true
      '@langchain/cohere':
        optional: true
      '@langchain/deepseek':
        optional: true
      '@langchain/google-genai':
        optional: true
      '@langchain/google-vertexai':
        optional: true
      '@langchain/google-vertexai-web':
        optional: true
      '@langchain/groq':
        optional: true
      '@langchain/mistralai':
        optional: true
      '@langchain/ollama':
        optional: true
      '@langchain/xai':
        optional: true
      axios:
        optional: true
      cheerio:
        optional: true
      handlebars:
        optional: true
      peggy:
        optional: true
      typeorm:
        optional: true

  langsmith@0.2.7:
    resolution: {integrity: sha512-9LFOp30cQ9K/7rzMt4USBI0SEKKhsH4l42ZERBPXOmDXnR5gYpsGFw8SZR0A6YLnc6vvoEmtr/XKel0Odq2UWw==}
    peerDependencies:
      openai: '*'
    peerDependenciesMeta:
      openai:
        optional: true

  langsmith@0.3.15:
    resolution: {integrity: sha512-cv3ebg0Hh0gRbl72cv/uzaZ+KOdfa2mGF1s74vmB2vlNVO/Ap/O9RYaHV+tpR8nwhGZ50R3ILnTOwSwGP+XQxw==}
    peerDependencies:
      openai: '*'
    peerDependenciesMeta:
      openai:
        optional: true

  lodash.includes@4.3.0:
    resolution: {integrity: sha512-W3Bx6mdkRTGtlJISOvVD/lbqjTlPPUDTMnlXZFnVwi9NKJ6tiAk6LVdlhZMm17VZisqhKcgzpO5Wz91PCt5b0w==}

  lodash.isboolean@3.0.3:
    resolution: {integrity: sha512-Bz5mupy2SVbPHURB98VAcw+aHh4vRV5IPNhILUCsOzRmsTmSQ17jIuqopAentWoehktxGd9e/hbIXq980/1QJg==}

  lodash.isinteger@4.0.4:
    resolution: {integrity: sha512-DBwtEWN2caHQ9/imiNeEA5ys1JoRtRfY3d7V9wkqtbycnAmTvRRmbHKDV4a0EYc678/dia0jrte4tjYwVBaZUA==}

  lodash.isnumber@3.0.3:
    resolution: {integrity: sha512-QYqzpfwO3/CWf3XP+Z+tkQsfaLL/EnUlXWVkIk5FUPc4sBdTehEqZONuyRt2P67PXAk+NXmTBcc97zw9t1FQrw==}

  lodash.isplainobject@4.0.6:
    resolution: {integrity: sha512-oSXzaWypCMHkPC3NvBEaPHf0KsA5mvPrOPgQWDsbg8n7orZ290M0BmC/jgRZ4vcJ6DTAhjrsSYgdsW/F+MFOBA==}

  lodash.isstring@4.0.1:
    resolution: {integrity: sha512-0wJxfxH1wgO3GrbuP+dTTk7op+6L41QCXbGINEmD+ny/G/eCqGzxyCsh7159S+mgDDcoarnBw6PC1PS5+wUGgw==}

  lodash.once@4.1.1:
    resolution: {integrity: sha512-Sb487aTOCr9drQVL8pIxOzVhafOjZN9UU54hiN8PU3uAiSV7lx1yYNpbNmex2PK6dSJoNTSJUUswT651yww3Mg==}

  mime-db@1.52.0:
    resolution: {integrity: sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==}
    engines: {node: '>= 0.6'}

  mime-types@2.1.35:
    resolution: {integrity: sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==}
    engines: {node: '>= 0.6'}

  ms@2.1.3:
    resolution: {integrity: sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==}

  mustache@4.2.0:
    resolution: {integrity: sha512-71ippSywq5Yb7/tVYyGbkBggbU8H3u5Rz56fH60jGFgr8uHwxs+aSKeqmluIVzM0m0kB7xQjKS6qPfd0b2ZoqQ==}
    hasBin: true

  node-domexception@1.0.0:
    resolution: {integrity: sha512-/jKZoMpw0F8GRwl4/eLROPA3cfcXtLApP0QzLmUT/HuPCZWyB7IY9ZrMeKw2O/nFIqPQB3PVM9aYm0F312AXDQ==}
    engines: {node: '>=10.5.0'}

  node-fetch@2.7.0:
    resolution: {integrity: sha512-c4FRfUm/dbcWZ7U+1Wq0AwCyFL+3nt2bEw05wfxSz+DWpWsitgmSgYmy2dQdWyKC1694ELPqMs/YzUSNozLt8A==}
    engines: {node: 4.x || >=6.0.0}
    peerDependencies:
      encoding: ^0.1.0
    peerDependenciesMeta:
      encoding:
        optional: true

  openai@4.73.0:
    resolution: {integrity: sha512-NZstV77w3CEol9KQTRBRQ15+Sw6nxVTicAULSjYO4wn9E5gw72Mtp3fAVaBFXyyVPws4241YmFG6ya4L8v03tA==}
    hasBin: true
    peerDependencies:
      zod: ^3.23.8
    peerDependenciesMeta:
      zod:
        optional: true

  openai@4.90.0:
    resolution: {integrity: sha512-YCuHMMycqtCg1B8G9ezkOF0j8UnBWD3Al/zYaelpuXwU1yhCEv+Y4n9G20MnyGy6cH4GsFwOMrgstQ+bgG1PtA==}
    hasBin: true
    peerDependencies:
      ws: ^8.18.0
      zod: ^3.23.8
    peerDependenciesMeta:
      ws:
        optional: true
      zod:
        optional: true

  openapi-types@12.1.3:
    resolution: {integrity: sha512-N4YtSYJqghVu4iek2ZUvcN/0aqH1kRDuNqzcycDxhOUpg7GdvLa2F3DgS6yBNhInhv2r/6I0Flkn7CqL8+nIcw==}

  p-finally@1.0.0:
    resolution: {integrity: sha512-LICb2p9CB7FS+0eR1oqWnHhp0FljGLZCWBE9aix0Uye9W8LTQPwMTYVGWQWIw9RdQiDg4+epXQODwIYJtSJaow==}
    engines: {node: '>=4'}

  p-queue@6.6.2:
    resolution: {integrity: sha512-RwFpb72c/BhQLEXIZ5K2e+AhgNVmIejGlTgiB9MzZ0e93GRvqZ7uSi0dvRF7/XIXDeNkra2fNHBxTyPDGySpjQ==}
    engines: {node: '>=8'}

  p-retry@4.6.2:
    resolution: {integrity: sha512-312Id396EbJdvRONlngUx0NydfrIQ5lsYu0znKVUzVvArzEIt08V1qhtyESbGVd1FGX7UKtiFp5uwKZdM8wIuQ==}
    engines: {node: '>=8'}

  p-timeout@3.2.0:
    resolution: {integrity: sha512-rhIwUycgwwKcP9yTOOFK/AKsAopjjCakVqLHePO3CC6Mir1Z99xT+R63jZxAT5lFZLa2inS5h+ZS2GvR99/FBg==}
    engines: {node: '>=8'}

  peek-readable@4.1.0:
    resolution: {integrity: sha512-ZI3LnwUv5nOGbQzD9c2iDG6toheuXSZP5esSHBjopsXH4dg19soufvpUGA3uohi5anFtGb2lhAVdHzH6R/Evvg==}
    engines: {node: '>=8'}

  playwright-core@1.51.1:
    resolution: {integrity: sha512-/crRMj8+j/Nq5s8QcvegseuyeZPxpQCZb6HNk3Sos3BlZyAknRjoyJPFWkpNn8v0+P3WiwqFF8P+zQo4eqiNuw==}
    engines: {node: '>=18'}
    hasBin: true

  playwright@1.51.1:
    resolution: {integrity: sha512-kkx+MB2KQRkyxjYPc3a0wLZZoDczmppyGJIvQ43l+aZihkaVvmu/21kiyaHeHjiFxjxNNFnUncKmcGIyOojsaw==}
    engines: {node: '>=18'}
    hasBin: true

  process@0.11.10:
    resolution: {integrity: sha512-cdGef/drWFoydD1JsMzuFf8100nZl+GT+yacc2bEced5f9Rjk4z+WtFUTBu9PhOi9j/jfmBPu0mMEY4wIdAF8A==}
    engines: {node: '>= 0.6.0'}

  proxy-from-env@1.1.0:
    resolution: {integrity: sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==}

  psl@1.15.0:
    resolution: {integrity: sha512-JZd3gMVBAVQkSs6HdNZo9Sdo0LNcQeMNP3CozBJb3JYC/QUYZTnKxP+f8oWRX4rHP5EurWxqAHTSwUCjlNKa1w==}

  punycode@2.3.1:
    resolution: {integrity: sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==}
    engines: {node: '>=6'}

  querystringify@2.2.0:
    resolution: {integrity: sha512-FIqgj2EUvTa7R50u0rGsyTftzjYmv/a3hO345bZNrqabNqjtgiDMgmo4mkUjd+nzU5oF3dClKqFIPUKybUyqoQ==}

  readable-stream@4.7.0:
    resolution: {integrity: sha512-oIGGmcpTLwPga8Bn6/Z75SVaH1z5dUut2ibSyAMVhmUggWpmDn2dapB0n7f8nwaSiRtepAsfJyfXIO5DCVAODg==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  readable-web-to-node-stream@3.0.4:
    resolution: {integrity: sha512-9nX56alTf5bwXQ3ZDipHJhusu9NTQJ/CVPtb/XHAJCXihZeitfJvIRS4GqQ/mfIoOE3IelHMrpayVrosdHBuLw==}
    engines: {node: '>=8'}

  requires-port@1.0.0:
    resolution: {integrity: sha512-KigOCHcocU3XODJxsu8i/j8T9tzT4adHiecwORRQ0ZZFcp7ahwXuRU1m+yuO90C5ZUyGeGfocHDI14M3L3yDAQ==}

  retry-axios@2.6.0:
    resolution: {integrity: sha512-pOLi+Gdll3JekwuFjXO3fTq+L9lzMQGcSq7M5gIjExcl3Gu1hd4XXuf5o3+LuSBsaULQH7DiNbsqPd1chVpQGQ==}
    engines: {node: '>=10.7.0'}
    peerDependencies:
      axios: '*'

  retry@0.13.1:
    resolution: {integrity: sha512-XQBQ3I8W1Cge0Seh+6gjj03LbmRFWuoszgK9ooCpwYIrhhoO80pfq4cUkU5DkknwfOfFteRwlZ56PYOGYyFWdg==}
    engines: {node: '>= 4'}

  safe-buffer@5.2.1:
    resolution: {integrity: sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==}

  semver@7.6.3:
    resolution: {integrity: sha512-oVekP1cKtI+CTDvHWYFUcMtsK/00wmAEfyqKfNdARm8u1wNVhSgaX7A8d4UuIlUI5e84iEwOhs7ZPYRmzU9U6A==}
    engines: {node: '>=10'}
    hasBin: true

  semver@7.7.1:
    resolution: {integrity: sha512-hlq8tAfn0m/61p4BVRcPzIGr6LKiMwo4VM6dGi6pt4qcRkmNzTcWq6eCEjEh+qXjkMDvPlOFFSGwQjoEa6gyMA==}
    engines: {node: '>=10'}
    hasBin: true

  simple-wcswidth@1.0.1:
    resolution: {integrity: sha512-xMO/8eNREtaROt7tJvWJqHBDTMFN4eiQ5I4JRMuilwfnFcV5W9u7RUkueNkdw0jPqGMX36iCywelS5yilTuOxg==}

  string_decoder@1.3.0:
    resolution: {integrity: sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==}

  strnum@1.0.5:
    resolution: {integrity: sha512-J8bbNyKKXl5qYcR36TIO8W3mVGVHrmmxsd5PAItGkmyzwJvybiw2IVq5nqd0i4LSNSkB/sx9VHllbfFdr9k1JA==}

  strtok3@6.3.0:
    resolution: {integrity: sha512-fZtbhtvI9I48xDSywd/somNqgUHl2L2cstmXCCif0itOf96jeW18MBSyrLuNicYQVkvpOxkZtkzujiTJ9LW5Jw==}
    engines: {node: '>=10'}

  supports-color@7.2.0:
    resolution: {integrity: sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==}
    engines: {node: '>=8'}

  token-types@4.2.1:
    resolution: {integrity: sha512-6udB24Q737UD/SDsKAHI9FCRP7Bqc9D/MQUV02ORQg5iskjtLJlZJNdN4kKtcdtwCeWIwIHDGaUsTsCCAa8sFQ==}
    engines: {node: '>=10'}

  tough-cookie@4.1.4:
    resolution: {integrity: sha512-Loo5UUvLD9ScZ6jh8beX1T6sO1w2/MpCRpEP7V280GKMVUQ0Jzar2U3UJPsrdbziLEMMhu3Ujnq//rhiFuIeag==}
    engines: {node: '>=6'}

  tr46@0.0.3:
    resolution: {integrity: sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw==}

  tslib@2.8.1:
    resolution: {integrity: sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==}

  typescript@5.6.3:
    resolution: {integrity: sha512-hjcS1mhfuyi4WW8IWtjP7brDrG2cuDZukyrYrSauoXGNgx0S7zceP07adYkJycEr56BOUTNPzbInooiN3fn1qw==}
    engines: {node: '>=14.17'}
    hasBin: true

  undici-types@5.26.5:
    resolution: {integrity: sha512-JlCMO+ehdEIKqlFxk6IfVoAUVmgz7cU7zD/h9XZ0qzeosSHmUJVOzSQvvYSYWXkFXC+IfLKSIffhv0sVZup6pA==}

  undici-types@6.19.8:
    resolution: {integrity: sha512-ve2KP6f/JnbPBFyobGHuerC9g1FYGn/F8n1LWTwNxCEzd6IfqTwUQcNXgEtmmQ6DlRrC1hrSrBnCZPokRrDHjw==}

  universalify@0.2.0:
    resolution: {integrity: sha512-CJ1QgKmNg3CwvAv/kOFmtnEN05f0D/cn9QntgNOQlQF9dgvVTHj3t+8JPdjqawCHk7V/KA+fbUqzZ9XWhcqPUg==}
    engines: {node: '>= 4.0.0'}

  url-parse@1.5.10:
    resolution: {integrity: sha512-WypcfiRhfeUP9vvF0j6rw0J3hrWrw6iZv3+22h6iRMJ/8z1Tj6XfLP4DsUix5MhMPnXpiHDoKyoZ/bdCkwBCiQ==}

  uuid@10.0.0:
    resolution: {integrity: sha512-8XkAphELsDnEGrDxUOHB3RGvXz6TeuYSGEZBOjtTtPm2lwhGBjLgOzLHB63IUWfBpNucQjND6d3AOudO+H3RWQ==}
    hasBin: true

  uuid@9.0.1:
    resolution: {integrity: sha512-b+1eJOlsR9K8HJpow9Ok3fiWOWSIcIzXodvv0rQjVoOVNpWMpxf1wZNpt4y9h10odCNrqnYp1OBzRktckBe3sA==}
    hasBin: true

  web-streams-polyfill@4.0.0-beta.3:
    resolution: {integrity: sha512-QW95TCTaHmsYfHDybGMwO5IJIM93I/6vTRk+daHTWFPhwh+C8Cg7j7XyKrwrj8Ib6vYXe0ocYNrmzY4xAAN6ug==}
    engines: {node: '>= 14'}

  webidl-conversions@3.0.1:
    resolution: {integrity: sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ==}

  whatwg-url@5.0.0:
    resolution: {integrity: sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw==}

  ws@8.18.1:
    resolution: {integrity: sha512-RKW2aJZMXeMxVpnZ6bck+RswznaxmzdULiBr6KY7XkTnW8uvt0iT9H5DkHUChXrc+uurzwa0rVI16n/Xzjdz1w==}
    engines: {node: '>=10.0.0'}
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: '>=5.0.2'
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true

  yaml@2.7.0:
    resolution: {integrity: sha512-+hSoy/QHluxmC9kCIJyL/uyFmLmc+e5CFR5Wa+bpIhIj85LVb9ZH2nVnqrHoSvKogwODv0ClqZkmiSSaIH5LTA==}
    engines: {node: '>= 14'}
    hasBin: true

  zod-to-json-schema@3.23.5:
    resolution: {integrity: sha512-5wlSS0bXfF/BrL4jPAbz9da5hDlDptdEppYfe+x4eIJ7jioqKG9uUxOwPzqof09u/XeVdrgFu29lZi+8XNDJtA==}
    peerDependencies:
      zod: ^3.23.3

  zod-to-json-schema@3.24.5:
    resolution: {integrity: sha512-/AuWwMP+YqiPbsJx5D6TfgRTc4kTLjsh5SOcd4bLsfUg2RcEXrFMJl1DGgdHy2aCfsIA/cr/1JM0xcB2GZji8g==}
    peerDependencies:
      zod: ^3.24.1

  zod@3.23.8:
    resolution: {integrity: sha512-XBx9AXhXktjUqnepgTiE5flcKIYWi/rme0Eaj+5Y0lftuGBq+jyRu/md4WnuxqgP1ubdpNCsYEYPxrzVHD8d6g==}

snapshots:

  '@anthropic-ai/sdk@0.27.3':
    dependencies:
      '@types/node': 18.19.64
      '@types/node-fetch': 2.6.12
      abort-controller: 3.0.0
      agentkeepalive: 4.5.0
      form-data-encoder: 1.7.2
      formdata-node: 4.4.1
      node-fetch: 2.7.0
    transitivePeerDependencies:
      - encoding

  '@browserbasehq/sdk@2.5.0':
    dependencies:
      '@types/node': 18.19.84
      '@types/node-fetch': 2.6.12
      abort-controller: 3.0.0
      agentkeepalive: 4.6.0
      form-data-encoder: 1.7.2
      formdata-node: 4.4.1
      node-fetch: 2.7.0
    transitivePeerDependencies:
      - encoding

  '@browserbasehq/stagehand@1.14.0(@playwright/test@1.51.1)(deepmerge@4.3.1)(dotenv@16.4.7)(openai@4.90.0(ws@8.18.1)(zod@3.23.8))(zod@3.23.8)':
    dependencies:
      '@anthropic-ai/sdk': 0.27.3
      '@browserbasehq/sdk': 2.5.0
      '@playwright/test': 1.51.1
      deepmerge: 4.3.1
      dotenv: 16.4.7
      openai: 4.90.0(ws@8.18.1)(zod@3.23.8)
      ws: 8.18.1
      zod: 3.23.8
      zod-to-json-schema: 3.24.5(zod@3.23.8)
    transitivePeerDependencies:
      - bufferutil
      - encoding
      - utf-8-validate

  '@copilotkit/sdk-js@1.8.3(@browserbasehq/sdk@2.5.0)(@browserbasehq/stagehand@1.14.0(@playwright/test@1.51.1)(deepmerge@4.3.1)(dotenv@16.4.7)(openai@4.90.0(ws@8.18.1)(zod@3.23.8))(zod@3.23.8))(@ibm-cloud/watsonx-ai@1.6.4(@langchain/core@0.3.18(openai@4.90.0(ws@8.18.1)(zod@3.23.8))))(@langchain/anthropic@0.3.8(@langchain/core@0.3.18(openai@4.90.0(ws@8.18.1)(zod@3.23.8))))(@langchain/google-genai@0.1.4(@langchain/core@0.3.18(openai@4.90.0(ws@8.18.1)(zod@3.23.8)))(zod@3.23.8))(axios@1.8.4)(fast-xml-parser@4.5.0)(ibm-cloud-sdk-core@5.3.2)(jsonwebtoken@9.0.2)(openai@4.90.0(ws@8.18.1)(zod@3.23.8))(playwright@1.51.1)(ws@8.18.1)':
    dependencies:
      '@copilotkit/shared': 1.8.3
      '@langchain/community': 0.3.40(@browserbasehq/sdk@2.5.0)(@browserbasehq/stagehand@1.14.0(@playwright/test@1.51.1)(deepmerge@4.3.1)(dotenv@16.4.7)(openai@4.90.0(ws@8.18.1)(zod@3.23.8))(zod@3.23.8))(@ibm-cloud/watsonx-ai@1.6.4(@langchain/core@0.3.18(openai@4.90.0(ws@8.18.1)(zod@3.23.8))))(@langchain/anthropic@0.3.8(@langchain/core@0.3.18(openai@4.90.0(ws@8.18.1)(zod@3.23.8))))(@langchain/core@0.3.18(openai@4.90.0(ws@8.18.1)(zod@3.23.8)))(@langchain/google-genai@0.1.4(@langchain/core@0.3.18(openai@4.90.0(ws@8.18.1)(zod@3.23.8)))(zod@3.23.8))(axios@1.8.4)(fast-xml-parser@4.5.0)(ibm-cloud-sdk-core@5.3.2)(jsonwebtoken@9.0.2)(openai@4.90.0(ws@8.18.1)(zod@3.23.8))(playwright@1.51.1)(ws@8.18.1)
      '@langchain/core': 0.3.18(openai@4.90.0(ws@8.18.1)(zod@3.23.8))
      '@langchain/langgraph': 0.2.44(@langchain/core@0.3.18(openai@4.90.0(ws@8.18.1)(zod@3.23.8)))
      langchain: 0.3.19(@langchain/anthropic@0.3.8(@langchain/core@0.3.18(openai@4.90.0(ws@8.18.1)(zod@3.23.8))))(@langchain/core@0.3.18(openai@4.90.0(ws@8.18.1)(zod@3.23.8)))(@langchain/google-genai@0.1.4(@langchain/core@0.3.18(openai@4.90.0(ws@8.18.1)(zod@3.23.8)))(zod@3.23.8))(axios@1.8.4)(openai@4.90.0(ws@8.18.1)(zod@3.23.8))
      zod: 3.23.8
    transitivePeerDependencies:
      - '@arcjet/redact'
      - '@aws-crypto/sha256-js'
      - '@aws-sdk/client-bedrock-agent-runtime'
      - '@aws-sdk/client-bedrock-runtime'
      - '@aws-sdk/client-dynamodb'
      - '@aws-sdk/client-kendra'
      - '@aws-sdk/client-lambda'
      - '@aws-sdk/client-s3'
      - '@aws-sdk/client-sagemaker-runtime'
      - '@aws-sdk/client-sfn'
      - '@aws-sdk/credential-provider-node'
      - '@aws-sdk/dsql-signer'
      - '@azure/search-documents'
      - '@azure/storage-blob'
      - '@browserbasehq/sdk'
      - '@browserbasehq/stagehand'
      - '@clickhouse/client'
      - '@cloudflare/ai'
      - '@datastax/astra-db-ts'
      - '@elastic/elasticsearch'
      - '@getmetal/metal-sdk'
      - '@getzep/zep-cloud'
      - '@getzep/zep-js'
      - '@gomomento/sdk'
      - '@gomomento/sdk-core'
      - '@google-ai/generativelanguage'
      - '@google-cloud/storage'
      - '@gradientai/nodejs-sdk'
      - '@huggingface/inference'
      - '@huggingface/transformers'
      - '@ibm-cloud/watsonx-ai'
      - '@lancedb/lancedb'
      - '@langchain/anthropic'
      - '@langchain/aws'
      - '@langchain/cerebras'
      - '@langchain/cohere'
      - '@langchain/deepseek'
      - '@langchain/google-genai'
      - '@langchain/google-vertexai'
      - '@langchain/google-vertexai-web'
      - '@langchain/groq'
      - '@langchain/mistralai'
      - '@langchain/ollama'
      - '@langchain/xai'
      - '@layerup/layerup-security'
      - '@libsql/client'
      - '@mendable/firecrawl-js'
      - '@mlc-ai/web-llm'
      - '@mozilla/readability'
      - '@neondatabase/serverless'
      - '@notionhq/client'
      - '@opensearch-project/opensearch'
      - '@pinecone-database/pinecone'
      - '@planetscale/database'
      - '@premai/prem-sdk'
      - '@qdrant/js-client-rest'
      - '@raycast/api'
      - '@rockset/client'
      - '@smithy/eventstream-codec'
      - '@smithy/protocol-http'
      - '@smithy/signature-v4'
      - '@smithy/util-utf8'
      - '@spider-cloud/spider-client'
      - '@supabase/supabase-js'
      - '@tensorflow-models/universal-sentence-encoder'
      - '@tensorflow/tfjs-converter'
      - '@tensorflow/tfjs-core'
      - '@upstash/ratelimit'
      - '@upstash/redis'
      - '@upstash/vector'
      - '@vercel/kv'
      - '@vercel/postgres'
      - '@writerai/writer-sdk'
      - '@xata.io/client'
      - '@zilliz/milvus2-sdk-node'
      - apify-client
      - assemblyai
      - axios
      - azion
      - better-sqlite3
      - cassandra-driver
      - cborg
      - cheerio
      - chromadb
      - closevector-common
      - closevector-node
      - closevector-web
      - cohere-ai
      - convex
      - crypto-js
      - d3-dsv
      - discord.js
      - dria
      - duck-duck-scrape
      - encoding
      - epub2
      - fast-xml-parser
      - firebase-admin
      - google-auth-library
      - googleapis
      - handlebars
      - hnswlib-node
      - html-to-text
      - ibm-cloud-sdk-core
      - ignore
      - interface-datastore
      - ioredis
      - it-all
      - jsdom
      - jsonwebtoken
      - llmonitor
      - lodash
      - lunary
      - mammoth
      - mariadb
      - mem0ai
      - mongodb
      - mysql2
      - neo4j-driver
      - notion-to-md
      - officeparser
      - openai
      - pdf-parse
      - peggy
      - pg
      - pg-copy-streams
      - pickleparser
      - playwright
      - portkey-ai
      - puppeteer
      - pyodide
      - redis
      - replicate
      - sonix-speech-recognition
      - srt-parser-2
      - typeorm
      - typesense
      - usearch
      - voy-search
      - weaviate-ts-client
      - web-auth-library
      - word-extractor
      - ws
      - youtubei.js

  '@copilotkit/shared@1.8.3':
    dependencies:
      '@segment/analytics-node': 2.2.1
      chalk: 4.1.2
      graphql: 16.10.0
      uuid: 10.0.0
      zod: 3.23.8
      zod-to-json-schema: 3.24.5(zod@3.23.8)
    transitivePeerDependencies:
      - encoding

  '@google/generative-ai@0.21.0': {}

  '@ibm-cloud/watsonx-ai@1.6.4(@langchain/core@0.3.18(openai@4.90.0(ws@8.18.1)(zod@3.23.8)))':
    dependencies:
      '@langchain/textsplitters': 0.1.0(@langchain/core@0.3.18(openai@4.90.0(ws@8.18.1)(zod@3.23.8)))
      '@types/node': 18.19.84
      extend: 3.0.2
      ibm-cloud-sdk-core: 5.3.2
    transitivePeerDependencies:
      - '@langchain/core'
      - supports-color

  '@langchain/anthropic@0.3.8(@langchain/core@0.3.18(openai@4.90.0(ws@8.18.1)(zod@3.23.8)))':
    dependencies:
      '@anthropic-ai/sdk': 0.27.3
      '@langchain/core': 0.3.18(openai@4.90.0(ws@8.18.1)(zod@3.23.8))
      fast-xml-parser: 4.5.0
      zod: 3.23.8
      zod-to-json-schema: 3.23.5(zod@3.23.8)
    transitivePeerDependencies:
      - encoding

  '@langchain/community@0.3.40(@browserbasehq/sdk@2.5.0)(@browserbasehq/stagehand@1.14.0(@playwright/test@1.51.1)(deepmerge@4.3.1)(dotenv@16.4.7)(openai@4.90.0(ws@8.18.1)(zod@3.23.8))(zod@3.23.8))(@ibm-cloud/watsonx-ai@1.6.4(@langchain/core@0.3.18(openai@4.90.0(ws@8.18.1)(zod@3.23.8))))(@langchain/anthropic@0.3.8(@langchain/core@0.3.18(openai@4.90.0(ws@8.18.1)(zod@3.23.8))))(@langchain/core@0.3.18(openai@4.90.0(ws@8.18.1)(zod@3.23.8)))(@langchain/google-genai@0.1.4(@langchain/core@0.3.18(openai@4.90.0(ws@8.18.1)(zod@3.23.8)))(zod@3.23.8))(axios@1.8.4)(fast-xml-parser@4.5.0)(ibm-cloud-sdk-core@5.3.2)(jsonwebtoken@9.0.2)(openai@4.90.0(ws@8.18.1)(zod@3.23.8))(playwright@1.51.1)(ws@8.18.1)':
    dependencies:
      '@browserbasehq/stagehand': 1.14.0(@playwright/test@1.51.1)(deepmerge@4.3.1)(dotenv@16.4.7)(openai@4.90.0(ws@8.18.1)(zod@3.23.8))(zod@3.23.8)
      '@ibm-cloud/watsonx-ai': 1.6.4(@langchain/core@0.3.18(openai@4.90.0(ws@8.18.1)(zod@3.23.8)))
      '@langchain/core': 0.3.18(openai@4.90.0(ws@8.18.1)(zod@3.23.8))
      '@langchain/openai': 0.3.14(@langchain/core@0.3.18(openai@4.90.0(ws@8.18.1)(zod@3.23.8)))
      binary-extensions: 2.3.0
      expr-eval: 2.0.2
      flat: 5.0.2
      ibm-cloud-sdk-core: 5.3.2
      js-yaml: 4.1.0
      langchain: 0.3.19(@langchain/anthropic@0.3.8(@langchain/core@0.3.18(openai@4.90.0(ws@8.18.1)(zod@3.23.8))))(@langchain/core@0.3.18(openai@4.90.0(ws@8.18.1)(zod@3.23.8)))(@langchain/google-genai@0.1.4(@langchain/core@0.3.18(openai@4.90.0(ws@8.18.1)(zod@3.23.8)))(zod@3.23.8))(axios@1.8.4)(openai@4.90.0(ws@8.18.1)(zod@3.23.8))
      langsmith: 0.3.15(openai@4.90.0(ws@8.18.1)(zod@3.23.8))
      openai: 4.90.0(ws@8.18.1)(zod@3.23.8)
      uuid: 10.0.0
      zod: 3.23.8
      zod-to-json-schema: 3.24.5(zod@3.23.8)
    optionalDependencies:
      '@browserbasehq/sdk': 2.5.0
      fast-xml-parser: 4.5.0
      jsonwebtoken: 9.0.2
      playwright: 1.51.1
      ws: 8.18.1
    transitivePeerDependencies:
      - '@langchain/anthropic'
      - '@langchain/aws'
      - '@langchain/cerebras'
      - '@langchain/cohere'
      - '@langchain/deepseek'
      - '@langchain/google-genai'
      - '@langchain/google-vertexai'
      - '@langchain/google-vertexai-web'
      - '@langchain/groq'
      - '@langchain/mistralai'
      - '@langchain/ollama'
      - '@langchain/xai'
      - axios
      - encoding
      - handlebars
      - peggy

  '@langchain/core@0.3.18(openai@4.90.0(ws@8.18.1)(zod@3.23.8))':
    dependencies:
      ansi-styles: 5.2.0
      camelcase: 6.3.0
      decamelize: 1.2.0
      js-tiktoken: 1.0.15
      langsmith: 0.2.7(openai@4.90.0(ws@8.18.1)(zod@3.23.8))
      mustache: 4.2.0
      p-queue: 6.6.2
      p-retry: 4.6.2
      uuid: 10.0.0
      zod: 3.23.8
      zod-to-json-schema: 3.23.5(zod@3.23.8)
    transitivePeerDependencies:
      - openai

  '@langchain/google-genai@0.1.4(@langchain/core@0.3.18(openai@4.90.0(ws@8.18.1)(zod@3.23.8)))(zod@3.23.8)':
    dependencies:
      '@google/generative-ai': 0.21.0
      '@langchain/core': 0.3.18(openai@4.90.0(ws@8.18.1)(zod@3.23.8))
      zod-to-json-schema: 3.23.5(zod@3.23.8)
    transitivePeerDependencies:
      - zod

  '@langchain/langgraph-checkpoint@0.0.15(@langchain/core@0.3.18(openai@4.90.0(ws@8.18.1)(zod@3.23.8)))':
    dependencies:
      '@langchain/core': 0.3.18(openai@4.90.0(ws@8.18.1)(zod@3.23.8))
      uuid: 10.0.0

  '@langchain/langgraph-sdk@0.0.36':
    dependencies:
      '@types/json-schema': 7.0.15
      p-queue: 6.6.2
      p-retry: 4.6.2
      uuid: 9.0.1

  '@langchain/langgraph@0.2.44(@langchain/core@0.3.18(openai@4.90.0(ws@8.18.1)(zod@3.23.8)))':
    dependencies:
      '@langchain/core': 0.3.18(openai@4.90.0(ws@8.18.1)(zod@3.23.8))
      '@langchain/langgraph-checkpoint': 0.0.15(@langchain/core@0.3.18(openai@4.90.0(ws@8.18.1)(zod@3.23.8)))
      '@langchain/langgraph-sdk': 0.0.36
      uuid: 10.0.0
      zod: 3.23.8

  '@langchain/openai@0.3.14(@langchain/core@0.3.18(openai@4.90.0(ws@8.18.1)(zod@3.23.8)))':
    dependencies:
      '@langchain/core': 0.3.18(openai@4.90.0(ws@8.18.1)(zod@3.23.8))
      js-tiktoken: 1.0.15
      openai: 4.73.0(zod@3.23.8)
      zod: 3.23.8
      zod-to-json-schema: 3.23.5(zod@3.23.8)
    transitivePeerDependencies:
      - encoding

  '@langchain/textsplitters@0.1.0(@langchain/core@0.3.18(openai@4.90.0(ws@8.18.1)(zod@3.23.8)))':
    dependencies:
      '@langchain/core': 0.3.18(openai@4.90.0(ws@8.18.1)(zod@3.23.8))
      js-tiktoken: 1.0.19

  '@lukeed/csprng@1.1.0': {}

  '@lukeed/uuid@2.0.1':
    dependencies:
      '@lukeed/csprng': 1.1.0

  '@playwright/test@1.51.1':
    dependencies:
      playwright: 1.51.1

  '@segment/analytics-core@1.8.1':
    dependencies:
      '@lukeed/uuid': 2.0.1
      '@segment/analytics-generic-utils': 1.2.0
      dset: 3.1.4
      tslib: 2.8.1

  '@segment/analytics-generic-utils@1.2.0':
    dependencies:
      tslib: 2.8.1

  '@segment/analytics-node@2.2.1':
    dependencies:
      '@lukeed/uuid': 2.0.1
      '@segment/analytics-core': 1.8.1
      '@segment/analytics-generic-utils': 1.2.0
      buffer: 6.0.3
      jose: 5.10.0
      node-fetch: 2.7.0
      tslib: 2.8.1
    transitivePeerDependencies:
      - encoding

  '@tokenizer/token@0.3.0': {}

  '@types/debug@4.1.12':
    dependencies:
      '@types/ms': 2.1.0

  '@types/html-to-text@9.0.4': {}

  '@types/json-schema@7.0.15': {}

  '@types/ms@2.1.0': {}

  '@types/node-fetch@2.6.12':
    dependencies:
      '@types/node': 22.9.1
      form-data: 4.0.1

  '@types/node@18.19.64':
    dependencies:
      undici-types: 5.26.5

  '@types/node@18.19.84':
    dependencies:
      undici-types: 5.26.5

  '@types/node@22.9.1':
    dependencies:
      undici-types: 6.19.8

  '@types/retry@0.12.0': {}

  '@types/tough-cookie@4.0.5': {}

  '@types/uuid@10.0.0': {}

  abort-controller@3.0.0:
    dependencies:
      event-target-shim: 5.0.1

  agentkeepalive@4.5.0:
    dependencies:
      humanize-ms: 1.2.1

  agentkeepalive@4.6.0:
    dependencies:
      humanize-ms: 1.2.1

  ansi-styles@4.3.0:
    dependencies:
      color-convert: 2.0.1

  ansi-styles@5.2.0: {}

  argparse@2.0.1: {}

  asynckit@0.4.0: {}

  axios@1.8.4(debug@4.4.0):
    dependencies:
      follow-redirects: 1.15.9(debug@4.4.0)
      form-data: 4.0.1
      proxy-from-env: 1.1.0
    transitivePeerDependencies:
      - debug

  base64-js@1.5.1: {}

  binary-extensions@2.3.0: {}

  buffer-equal-constant-time@1.0.1: {}

  buffer@6.0.3:
    dependencies:
      base64-js: 1.5.1
      ieee754: 1.2.1

  camelcase@6.3.0: {}

  chalk@4.1.2:
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0

  color-convert@2.0.1:
    dependencies:
      color-name: 1.1.4

  color-name@1.1.4: {}

  combined-stream@1.0.8:
    dependencies:
      delayed-stream: 1.0.0

  commander@10.0.1: {}

  console-table-printer@2.12.1:
    dependencies:
      simple-wcswidth: 1.0.1

  debug@4.4.0:
    dependencies:
      ms: 2.1.3

  decamelize@1.2.0: {}

  deepmerge@4.3.1: {}

  delayed-stream@1.0.0: {}

  dotenv@16.4.7: {}

  dset@3.1.4: {}

  ecdsa-sig-formatter@1.0.11:
    dependencies:
      safe-buffer: 5.2.1

  event-target-shim@5.0.1: {}

  eventemitter3@4.0.7: {}

  events@3.3.0: {}

  expr-eval@2.0.2: {}

  extend@3.0.2: {}

  fast-xml-parser@4.5.0:
    dependencies:
      strnum: 1.0.5

  file-type@16.5.4:
    dependencies:
      readable-web-to-node-stream: 3.0.4
      strtok3: 6.3.0
      token-types: 4.2.1

  flat@5.0.2: {}

  follow-redirects@1.15.9(debug@4.4.0):
    optionalDependencies:
      debug: 4.4.0

  form-data-encoder@1.7.2: {}

  form-data@4.0.0:
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      mime-types: 2.1.35

  form-data@4.0.1:
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      mime-types: 2.1.35

  formdata-node@4.4.1:
    dependencies:
      node-domexception: 1.0.0
      web-streams-polyfill: 4.0.0-beta.3

  fsevents@2.3.2:
    optional: true

  graphql@16.10.0: {}

  has-flag@4.0.0: {}

  humanize-ms@1.2.1:
    dependencies:
      ms: 2.1.3

  ibm-cloud-sdk-core@5.3.2:
    dependencies:
      '@types/debug': 4.1.12
      '@types/node': 18.19.84
      '@types/tough-cookie': 4.0.5
      axios: 1.8.4(debug@4.4.0)
      camelcase: 6.3.0
      debug: 4.4.0
      dotenv: 16.4.7
      extend: 3.0.2
      file-type: 16.5.4
      form-data: 4.0.0
      isstream: 0.1.2
      jsonwebtoken: 9.0.2
      mime-types: 2.1.35
      retry-axios: 2.6.0(axios@1.8.4(debug@4.4.0))
      tough-cookie: 4.1.4
    transitivePeerDependencies:
      - supports-color

  ieee754@1.2.1: {}

  isstream@0.1.2: {}

  jose@5.10.0: {}

  js-tiktoken@1.0.15:
    dependencies:
      base64-js: 1.5.1

  js-tiktoken@1.0.19:
    dependencies:
      base64-js: 1.5.1

  js-yaml@4.1.0:
    dependencies:
      argparse: 2.0.1

  jsonpointer@5.0.1: {}

  jsonwebtoken@9.0.2:
    dependencies:
      jws: 3.2.2
      lodash.includes: 4.3.0
      lodash.isboolean: 3.0.3
      lodash.isinteger: 4.0.4
      lodash.isnumber: 3.0.3
      lodash.isplainobject: 4.0.6
      lodash.isstring: 4.0.1
      lodash.once: 4.1.1
      ms: 2.1.3
      semver: 7.7.1

  jwa@1.4.1:
    dependencies:
      buffer-equal-constant-time: 1.0.1
      ecdsa-sig-formatter: 1.0.11
      safe-buffer: 5.2.1

  jws@3.2.2:
    dependencies:
      jwa: 1.4.1
      safe-buffer: 5.2.1

  langchain@0.3.19(@langchain/anthropic@0.3.8(@langchain/core@0.3.18(openai@4.90.0(ws@8.18.1)(zod@3.23.8))))(@langchain/core@0.3.18(openai@4.90.0(ws@8.18.1)(zod@3.23.8)))(@langchain/google-genai@0.1.4(@langchain/core@0.3.18(openai@4.90.0(ws@8.18.1)(zod@3.23.8)))(zod@3.23.8))(axios@1.8.4)(openai@4.90.0(ws@8.18.1)(zod@3.23.8)):
    dependencies:
      '@langchain/core': 0.3.18(openai@4.90.0(ws@8.18.1)(zod@3.23.8))
      '@langchain/openai': 0.3.14(@langchain/core@0.3.18(openai@4.90.0(ws@8.18.1)(zod@3.23.8)))
      '@langchain/textsplitters': 0.1.0(@langchain/core@0.3.18(openai@4.90.0(ws@8.18.1)(zod@3.23.8)))
      js-tiktoken: 1.0.19
      js-yaml: 4.1.0
      jsonpointer: 5.0.1
      langsmith: 0.3.15(openai@4.90.0(ws@8.18.1)(zod@3.23.8))
      openapi-types: 12.1.3
      p-retry: 4.6.2
      uuid: 10.0.0
      yaml: 2.7.0
      zod: 3.23.8
      zod-to-json-schema: 3.24.5(zod@3.23.8)
    optionalDependencies:
      '@langchain/anthropic': 0.3.8(@langchain/core@0.3.18(openai@4.90.0(ws@8.18.1)(zod@3.23.8)))
      '@langchain/google-genai': 0.1.4(@langchain/core@0.3.18(openai@4.90.0(ws@8.18.1)(zod@3.23.8)))(zod@3.23.8)
      axios: 1.8.4(debug@4.4.0)
    transitivePeerDependencies:
      - encoding
      - openai

  langsmith@0.2.7(openai@4.90.0(ws@8.18.1)(zod@3.23.8)):
    dependencies:
      '@types/uuid': 10.0.0
      commander: 10.0.1
      p-queue: 6.6.2
      p-retry: 4.6.2
      semver: 7.6.3
      uuid: 10.0.0
    optionalDependencies:
      openai: 4.90.0(ws@8.18.1)(zod@3.23.8)

  langsmith@0.3.15(openai@4.90.0(ws@8.18.1)(zod@3.23.8)):
    dependencies:
      '@types/uuid': 10.0.0
      chalk: 4.1.2
      console-table-printer: 2.12.1
      p-queue: 6.6.2
      p-retry: 4.6.2
      semver: 7.7.1
      uuid: 10.0.0
    optionalDependencies:
      openai: 4.90.0(ws@8.18.1)(zod@3.23.8)

  lodash.includes@4.3.0: {}

  lodash.isboolean@3.0.3: {}

  lodash.isinteger@4.0.4: {}

  lodash.isnumber@3.0.3: {}

  lodash.isplainobject@4.0.6: {}

  lodash.isstring@4.0.1: {}

  lodash.once@4.1.1: {}

  mime-db@1.52.0: {}

  mime-types@2.1.35:
    dependencies:
      mime-db: 1.52.0

  ms@2.1.3: {}

  mustache@4.2.0: {}

  node-domexception@1.0.0: {}

  node-fetch@2.7.0:
    dependencies:
      whatwg-url: 5.0.0

  openai@4.73.0(zod@3.23.8):
    dependencies:
      '@types/node': 18.19.64
      '@types/node-fetch': 2.6.12
      abort-controller: 3.0.0
      agentkeepalive: 4.5.0
      form-data-encoder: 1.7.2
      formdata-node: 4.4.1
      node-fetch: 2.7.0
    optionalDependencies:
      zod: 3.23.8
    transitivePeerDependencies:
      - encoding

  openai@4.90.0(ws@8.18.1)(zod@3.23.8):
    dependencies:
      '@types/node': 18.19.84
      '@types/node-fetch': 2.6.12
      abort-controller: 3.0.0
      agentkeepalive: 4.6.0
      form-data-encoder: 1.7.2
      formdata-node: 4.4.1
      node-fetch: 2.7.0
    optionalDependencies:
      ws: 8.18.1
      zod: 3.23.8
    transitivePeerDependencies:
      - encoding

  openapi-types@12.1.3: {}

  p-finally@1.0.0: {}

  p-queue@6.6.2:
    dependencies:
      eventemitter3: 4.0.7
      p-timeout: 3.2.0

  p-retry@4.6.2:
    dependencies:
      '@types/retry': 0.12.0
      retry: 0.13.1

  p-timeout@3.2.0:
    dependencies:
      p-finally: 1.0.0

  peek-readable@4.1.0: {}

  playwright-core@1.51.1: {}

  playwright@1.51.1:
    dependencies:
      playwright-core: 1.51.1
    optionalDependencies:
      fsevents: 2.3.2

  process@0.11.10: {}

  proxy-from-env@1.1.0: {}

  psl@1.15.0:
    dependencies:
      punycode: 2.3.1

  punycode@2.3.1: {}

  querystringify@2.2.0: {}

  readable-stream@4.7.0:
    dependencies:
      abort-controller: 3.0.0
      buffer: 6.0.3
      events: 3.3.0
      process: 0.11.10
      string_decoder: 1.3.0

  readable-web-to-node-stream@3.0.4:
    dependencies:
      readable-stream: 4.7.0

  requires-port@1.0.0: {}

  retry-axios@2.6.0(axios@1.8.4(debug@4.4.0)):
    dependencies:
      axios: 1.8.4(debug@4.4.0)

  retry@0.13.1: {}

  safe-buffer@5.2.1: {}

  semver@7.6.3: {}

  semver@7.7.1: {}

  simple-wcswidth@1.0.1: {}

  string_decoder@1.3.0:
    dependencies:
      safe-buffer: 5.2.1

  strnum@1.0.5: {}

  strtok3@6.3.0:
    dependencies:
      '@tokenizer/token': 0.3.0
      peek-readable: 4.1.0

  supports-color@7.2.0:
    dependencies:
      has-flag: 4.0.0

  token-types@4.2.1:
    dependencies:
      '@tokenizer/token': 0.3.0
      ieee754: 1.2.1

  tough-cookie@4.1.4:
    dependencies:
      psl: 1.15.0
      punycode: 2.3.1
      universalify: 0.2.0
      url-parse: 1.5.10

  tr46@0.0.3: {}

  tslib@2.8.1: {}

  typescript@5.6.3: {}

  undici-types@5.26.5: {}

  undici-types@6.19.8: {}

  universalify@0.2.0: {}

  url-parse@1.5.10:
    dependencies:
      querystringify: 2.2.0
      requires-port: 1.0.0

  uuid@10.0.0: {}

  uuid@9.0.1: {}

  web-streams-polyfill@4.0.0-beta.3: {}

  webidl-conversions@3.0.1: {}

  whatwg-url@5.0.0:
    dependencies:
      tr46: 0.0.3
      webidl-conversions: 3.0.1

  ws@8.18.1: {}

  yaml@2.7.0: {}

  zod-to-json-schema@3.23.5(zod@3.23.8):
    dependencies:
      zod: 3.23.8

  zod-to-json-schema@3.24.5(zod@3.23.8):
    dependencies:
      zod: 3.23.8

  zod@3.23.8: {}
