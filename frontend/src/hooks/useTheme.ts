import { useState, useEffect, useCallback } from 'react';

export type Theme = "blue" | "orange" | "green" | "purple" | "red" | "pink" | "dark" | "light";

export const themes = {
  blue: {
    primary: "bg-blue-600",
    secondary: "bg-blue-50", 
    text: "text-blue-600",
    border: "border-blue-600",
    gradient: "from-blue-400 via-blue-500 to-blue-600",
    accent: "bg-blue-500",
    hover: "hover:bg-blue-700"
  },
  orange: {
    primary: "bg-orange-600",
    secondary: "bg-orange-50",
    text: "text-orange-600", 
    border: "border-orange-600",
    gradient: "from-orange-400 via-orange-500 to-orange-600",
    accent: "bg-orange-500",
    hover: "hover:bg-orange-700"
  },
  green: {
    primary: "bg-green-600",
    secondary: "bg-green-50",
    text: "text-green-600",
    border: "border-green-600", 
    gradient: "from-green-400 via-green-500 to-green-600",
    accent: "bg-green-500",
    hover: "hover:bg-green-700"
  },
  purple: {
    primary: "bg-purple-600",
    secondary: "bg-purple-50",
    text: "text-purple-600",
    border: "border-purple-600",
    gradient: "from-purple-400 via-purple-500 to-purple-600",
    accent: "bg-purple-500",
    hover: "hover:bg-purple-700"
  },
  red: {
    primary: "bg-red-600", 
    secondary: "bg-red-50",
    text: "text-red-600",
    border: "border-red-600",
    gradient: "from-red-400 via-red-500 to-red-600",
    accent: "bg-red-500",
    hover: "hover:bg-red-700"
  },
  pink: {
    primary: "bg-pink-600",
    secondary: "bg-pink-50",
    text: "text-pink-600",
    border: "border-pink-600", 
    gradient: "from-pink-400 via-pink-500 to-pink-600",
    accent: "bg-pink-500",
    hover: "hover:bg-pink-700"
  },
  dark: {
    primary: "bg-gray-800",
    secondary: "bg-gray-900",
    text: "text-gray-100",
    border: "border-gray-600",
    gradient: "from-gray-700 via-gray-800 to-gray-900",
    accent: "bg-gray-700",
    hover: "hover:bg-gray-900"
  },
  light: {
    primary: "bg-gray-100",
    secondary: "bg-gray-50",
    text: "text-gray-800",
    border: "border-gray-300",
    gradient: "from-gray-50 via-gray-100 to-gray-200",
    accent: "bg-gray-200",
    hover: "hover:bg-gray-300"
  }
};

export interface UseThemeReturn {
  currentTheme: Theme;
  theme: typeof themes[Theme];
  isDarkMode: boolean;
  isAnimating: boolean;
  message: string;
  handleThemeChange: (newTheme: Theme) => void;
  toggleDarkMode: () => void;
  setMessage: (message: string) => void;
}

export const useTheme = (initialTheme: Theme = "blue"): UseThemeReturn => {
  const [currentTheme, setCurrentTheme] = useState<Theme>(initialTheme);
  const [message, setMessage] = useState("");
  const [isAnimating, setIsAnimating] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(false);

  // Persistance du thème dans localStorage
  useEffect(() => {
    const savedTheme = localStorage.getItem('clade4ai-theme') as Theme;
    const savedDarkMode = localStorage.getItem('clade4ai-dark-mode') === 'true';
    
    if (savedTheme && themes[savedTheme]) {
      setCurrentTheme(savedTheme);
    }
    setIsDarkMode(savedDarkMode);
  }, []);

  useEffect(() => {
    localStorage.setItem('clade4ai-theme', currentTheme);
  }, [currentTheme]);

  useEffect(() => {
    localStorage.setItem('clade4ai-dark-mode', isDarkMode.toString());
  }, [isDarkMode]);

  const handleThemeChange = useCallback((newTheme: Theme) => {
    setIsAnimating(true);
    setCurrentTheme(newTheme);
    setMessage(`Thème changé vers ${newTheme} ! ✨`);
    
    // Déclencher une animation globale
    document.body.style.transform = 'scale(0.98)';
    document.body.style.transition = 'transform 0.2s ease';
    
    setTimeout(() => {
      document.body.style.transform = 'scale(1)';
      setIsAnimating(false);
      setTimeout(() => setMessage(""), 2000);
    }, 200);
  }, []);

  const toggleDarkMode = useCallback(() => {
    setIsDarkMode(prev => !prev);
    setMessage(isDarkMode ? "Mode clair activé ☀️" : "Mode sombre activé 🌙");
    setTimeout(() => setMessage(""), 2000);
  }, [isDarkMode]);

  const theme = themes[currentTheme];

  return {
    currentTheme,
    theme,
    isDarkMode,
    isAnimating,
    message,
    handleThemeChange,
    toggleDarkMode,
    setMessage
  };
};
