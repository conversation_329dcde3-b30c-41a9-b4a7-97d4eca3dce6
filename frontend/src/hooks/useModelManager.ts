"use client";

import { useState, useEffect, useCallback } from 'react';
import { AIModel, AI_MODELS } from '../components/ModelSelector';

interface ModelState {
  currentModel: AIModel;
  isConnected: boolean;
  lastResponse?: Date;
  error?: string | null;
  isLoading: boolean;
}

interface UseModelManagerReturn extends ModelState {
  changeModel: (model: AIModel) => Promise<void>;
  testConnection: () => Promise<boolean>;
  clearError: () => void;
  refreshStatus: () => Promise<void>;
}

export const useModelManager = (): UseModelManagerReturn => {
  const [state, setState] = useState<ModelState>({
    currentModel: AI_MODELS[1], // Default to GPT-3.5
    isConnected: false,
    lastResponse: undefined,
    error: null,
    isLoading: false
  });

  // Load saved model from localStorage on mount
  useEffect(() => {
    const savedModelId = localStorage.getItem('copilotkit-selected-model');
    if (savedModelId) {
      const savedModel = AI_MODELS.find(m => m.id === savedModelId);
      if (savedModel) {
        setState(prev => ({
          ...prev,
          currentModel: savedModel
        }));
      }
    }
  }, []);

  // Test connection to the model
  const testConnection = useCallback(async (): Promise<boolean> => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      // Send a test request to the CopilotKit API
      const response = await fetch('/api/copilotkit/test-model', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: state.currentModel.id,
          message: 'Test connection'
        }),
      });

      if (response.ok) {
        setState(prev => ({
          ...prev,
          isConnected: true,
          lastResponse: new Date(),
          error: null,
          isLoading: false
        }));
        return true;
      } else {
        const errorData = await response.json();
        setState(prev => ({
          ...prev,
          isConnected: false,
          error: errorData.error || 'Erreur de connexion',
          isLoading: false
        }));
        return false;
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        isConnected: false,
        error: error instanceof Error ? error.message : 'Erreur de réseau',
        isLoading: false
      }));
      return false;
    }
  }, [state.currentModel.id]);

  // Change the current model
  const changeModel = useCallback(async (model: AIModel): Promise<void> => {
    setState(prev => ({
      ...prev,
      currentModel: model,
      isLoading: true,
      error: null,
      isConnected: false
    }));

    // Save to localStorage
    localStorage.setItem('copilotkit-selected-model', model.id);

    // Test the new model
    try {
      const response = await fetch('/api/copilotkit/change-model', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          modelId: model.id
        }),
      });

      if (response.ok) {
        setState(prev => ({
          ...prev,
          isConnected: true,
          lastResponse: new Date(),
          error: null,
          isLoading: false
        }));
      } else {
        const errorData = await response.json();
        setState(prev => ({
          ...prev,
          isConnected: false,
          error: errorData.error || 'Impossible de changer le modèle',
          isLoading: false
        }));
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        isConnected: false,
        error: error instanceof Error ? error.message : 'Erreur lors du changement de modèle',
        isLoading: false
      }));
    }
  }, []);

  // Clear error state
  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  // Refresh connection status
  const refreshStatus = useCallback(async (): Promise<void> => {
    await testConnection();
  }, [testConnection]);

  // Auto-test connection when model changes
  useEffect(() => {
    const timer = setTimeout(() => {
      testConnection();
    }, 1000);

    return () => clearTimeout(timer);
  }, [state.currentModel.id, testConnection]);

  // Periodic status check (every 30 seconds)
  useEffect(() => {
    const interval = setInterval(() => {
      if (state.isConnected && !state.isLoading) {
        testConnection();
      }
    }, 30000);

    return () => clearInterval(interval);
  }, [state.isConnected, state.isLoading, testConnection]);

  return {
    ...state,
    changeModel,
    testConnection,
    clearError,
    refreshStatus
  };
};

export default useModelManager;
