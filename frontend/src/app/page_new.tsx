"use client";

import { useState } from "react";
import { CopilotPopup } from "@copilotkit/react-ui";

type Theme = "blue" | "orange" | "green" | "purple" | "red" | "pink";

const themes = {
  blue: {
    primary: "bg-blue-600",
    secondary: "bg-blue-100", 
    text: "text-blue-600",
    border: "border-blue-600",
    gradient: "from-blue-400 to-blue-600"
  },
  orange: {
    primary: "bg-orange-600",
    secondary: "bg-orange-100",
    text: "text-orange-600", 
    border: "border-orange-600",
    gradient: "from-orange-400 to-orange-600"
  },
  green: {
    primary: "bg-green-600",
    secondary: "bg-green-100",
    text: "text-green-600",
    border: "border-green-600", 
    gradient: "from-green-400 to-green-600"
  },
  purple: {
    primary: "bg-purple-600",
    secondary: "bg-purple-100",
    text: "text-purple-600",
    border: "border-purple-600",
    gradient: "from-purple-400 to-purple-600"
  },
  red: {
    primary: "bg-red-600", 
    secondary: "bg-red-100",
    text: "text-red-600",
    border: "border-red-600",
    gradient: "from-red-400 to-red-600"
  },
  pink: {
    primary: "bg-pink-600",
    secondary: "bg-pink-100",
    text: "text-pink-600",
    border: "border-pink-600", 
    gradient: "from-pink-400 to-pink-600"
  }
};

export default function Home() {
  const [currentTheme, setCurrentTheme] = useState<Theme>("blue");
  const [message, setMessage] = useState("");

  const theme = themes[currentTheme];

  return (
    <div className="min-h-screen transition-all duration-500">
      {/* Header avec gradient dynamique */}
      <header className={`bg-gradient-to-r ${theme.gradient} text-white p-6`}>
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-bold mb-2">CopilotKit Demo - Drone Shop</h1>
          <p className="text-xl opacity-90">
            Utilisez l&apos;assistant AI pour changer le thème de l&apos;interface !
          </p>
        </div>
      </header>

      {/* Message de confirmation */}
      {message && (
        <div className={`${theme.secondary} ${theme.text} p-4 text-center font-medium`}>
          {message}
        </div>
      )}

      {/* Contenu principal */}
      <main className="max-w-4xl mx-auto p-6">
        {/* Section thème actuel */}
        <div className={`${theme.secondary} rounded-lg p-6 mb-8`}>
          <h2 className={`${theme.text} text-2xl font-bold mb-4`}>
            Thème actuel : {currentTheme}
          </h2>
          <p className="text-gray-700 mb-4">
            Testez l&apos;assistant CopilotKit avec ces commandes :
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className={`${theme.text} text-xl font-bold mb-3`}>
                Commandes vocales suggérées :
              </h3>
              <ul className="space-y-2 text-gray-700">
                <li>• &quot;Set the theme to orange&quot;</li>
                <li>• &quot;Change the theme to green&quot;</li>
                <li>• &quot;Use the purple theme&quot;</li>
                <li>• &quot;Switch to red theme&quot;</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Grille des thèmes disponibles */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold mb-6 text-gray-800">Thèmes disponibles</h2>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {Object.entries(themes).map(([name, themeConfig]) => (
              <button
                key={name}
                onClick={() => {
                  setCurrentTheme(name as Theme);
                  setMessage(`Thème changé vers ${name} !`);
                  setTimeout(() => setMessage(""), 3000);
                }}
                className={`
                  ${themeConfig.primary} hover:opacity-80 
                  text-white p-4 rounded-lg transition-all duration-200
                  ${currentTheme === name ? 'ring-4 ring-gray-400' : ''}
                `}
              >
                <div className="text-center">
                  <div className="text-lg font-semibold capitalize">{name}</div>
                  <div className="text-sm opacity-80">Cliquez pour tester</div>
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Informations sur le projet */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-xl font-bold mb-4 text-gray-800">
              Fonctionnalités CopilotKit
            </h3>
            <ul className="space-y-2 text-gray-700">
              <li>• Chat AI interactif</li>
              <li>• Commandes vocales</li>
              <li>• Changement de thème dynamique</li>
              <li>• Intégration avec LangGraph</li>
            </ul>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-xl font-bold mb-4 text-gray-800">
              Architecture technique
            </h3>
            <ul className="space-y-2 text-gray-700">
              <li>• Next.js 15 avec Turbopack</li>
              <li>• CopilotKit React UI</li>
              <li>• Agent LangGraph personnalisé</li>
              <li>• Tailwind CSS pour le styling</li>
            </ul>
          </div>
        </div>

        {/* Instructions d'utilisation */}
        <div className="mt-8 bg-gray-50 rounded-lg p-6">
          <h3 className="text-xl font-bold mb-4 text-gray-800">
            Comment tester l&apos;assistant ?
          </h3>
          <ol className="space-y-2 text-gray-700">
            <li>1. Cliquez sur l&apos;icône CopilotKit en bas à droite</li>
            <li>2. Tapez ou dites : &quot;Set the theme to orange&quot;</li>
            <li>3. Observez le changement de thème en temps réel</li>
          </ol>
        </div>
      </main>

      {/* Assistant CopilotKit */}
      <CopilotPopup
        instructions="Bonjour ! Je peux vous aider à changer le thème de l'interface. Essayez de dire 'Change le thème en orange' ou 'Set the theme to green'."
        labels={{
          title: "Assistant Thème",
          initial: "Salut ! Comment puis-je vous aider ?",
        }}
      />
    </div>
  );
}
