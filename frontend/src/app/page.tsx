"use client";

import { CopilotChat } from "@copilotkit/react-ui";
import { useTheme, themes, Theme } from "../hooks/useTheme";
import { ThemeCard, FeatureCard, QuickActionButton } from "../components/ui/theme-components";
import { ModernHeader, Notification, PerformanceMetrics } from "../components/ui/layout-components";

export default function Home() {
  const { currentTheme, theme, isDarkMode, isAnimating, message: themeMessage, handleThemeChange, toggleDarkMode } = useTheme();

  const displayMessage = themeMessage;

  return (
    <div className={`min-h-screen transition-all duration-700 glass-container ${isDarkMode ? 'dark bg-gray-900' : 'bg-gray-50'}`}>
      {/* Header moderne avec composant */}
      <ModernHeader 
        theme={theme}
        isDarkMode={isDarkMode}
        onToggleDarkMode={toggleDarkMode}
        currentTheme={currentTheme}
      />

      {/* Message de confirmation avec animations améliorées */}
      {displayMessage && (
        <Notification 
          message={displayMessage}
          theme={theme}
        />
      )}

      {/* Contenu principal avec layout amélioré */}
      <main className={`max-w-6xl mx-auto p-6 ${isAnimating ? 'animate-pulse' : ''}`}>
        
        {/* Section héro modernisée */}
        <div className={`${theme.secondary} rounded-2xl p-8 mb-8 shadow-lg border-l-4 ${theme.border} relative overflow-hidden glass-card slide-in`}>
          <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-transparent to-white opacity-5 rounded-full floating"></div>
          <div className="relative z-10">
            <h2 className={`${theme.text} text-3xl font-bold mb-4 flex items-center gap-3`}>
              <span className="text-4xl floating">🎨</span>
              Contrôle de thème intelligent
            </h2>
            <p className={`text-gray-700 mb-6 text-lg ${isDarkMode ? 'text-gray-300' : ''}`}>
              Utilisez l&apos;assistant IA pour personnaliser l&apos;interface en temps réel
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className={`bg-white ${isDarkMode ? 'bg-gray-800' : ''} rounded-xl p-6 shadow-md glass-card hover-glow`}>
                <h3 className={`${theme.text} text-xl font-bold mb-4 flex items-center gap-2`}>
                  <span className="floating">🗣️</span> Commandes vocales
                </h3>
                <ul className={`space-y-3 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  <li className="flex items-center gap-2">
                    <span className="w-2 h-2 bg-green-500 rounded-full glow-pulse"></span>
                    &quot;Change le thème en orange&quot;
                  </li>
                  <li className="flex items-center gap-2">
                    <span className="w-2 h-2 bg-blue-500 rounded-full glow-pulse"></span>
                    &quot;Set the theme to purple&quot;
                  </li>
                  <li className="flex items-center gap-2">
                    <span className="w-2 h-2 bg-red-500 rounded-full glow-pulse"></span>
                    &quot;Utilise le thème sombre&quot;
                  </li>
                </ul>
              </div>
              
              <div className={`bg-white ${isDarkMode ? 'bg-gray-800' : ''} rounded-xl p-6 shadow-md glass-card hover-glow`}>
                <h3 className={`${theme.text} text-xl font-bold mb-4 flex items-center gap-2`}>
                  <span className="floating">⚡</span> Actions rapides
                </h3>
                <div className="space-y-3">
                  <QuickActionButton
                    onClick={() => handleThemeChange("orange")}
                    label="Mode énergique"
                    icon="🔥"
                    variant="primary"
                  />
                  <QuickActionButton
                    onClick={() => handleThemeChange("dark")}
                    label="Mode nocturne"
                    icon="🌙"
                    variant="secondary"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Grille des thèmes interactive avec composants */}
        <div className="mb-8 slide-in">
          <h2 className={`text-3xl font-bold mb-6 ${isDarkMode ? 'text-white' : 'text-gray-800'} flex items-center gap-3`}>
            <span className="text-4xl floating">🎨</span>
            Palette de thèmes
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
            {Object.entries(themes).map(([name, themeConfig]) => (
              <ThemeCard
                key={name}
                name={name}
                config={themeConfig}
                isActive={currentTheme === name}
                isAnimating={isAnimating}
                onClick={() => handleThemeChange(name as Theme)}
              />
            ))}
          </div>
        </div>

        {/* Cards d'information avec composants modulaires */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8 slide-in">
          <FeatureCard
            icon="🚀"
            title="Fonctionnalités IA"
            description="Découvrez les capacités avancées de notre assistant intelligent"
            features={[
              "Chat IA interactif",
              "Commandes vocales", 
              "Changement dynamique",
              "Intégration LangGraph"
            ]}
            theme={theme}
            isDarkMode={isDarkMode}
          />
          
          <FeatureCard
            icon="⚡"
            title="Technologies"
            description="Stack technique moderne et performante"
            features={[
              "Next.js 15 + Turbopack",
              "CopilotKit React UI",
              "Agent LangGraph",
              "Tailwind CSS"
            ]}
            theme={theme}
            isDarkMode={isDarkMode}
          />
          
          <FeatureCard
            icon="💡"
            title="Guide rapide"
            description="Commencez à utiliser l'interface en quelques étapes"
            features={[
              "Ouvrez l'assistant en bas à droite",
              "Dites: \"Change le thème en orange\"",
              "Regardez la magie opérer ! ✨"
            ]}
            theme={theme}
            isDarkMode={isDarkMode}
          />
        </div>

        {/* Section performances avec composant */}
        <PerformanceMetrics 
          theme={theme}
          isDarkMode={isDarkMode}
        />
      </main>

      {/* Assistant CopilotKit amélioré avec glassmorphism */}
      <div className="fixed bottom-4 right-4 w-96 h-96 glass-card rounded-2xl shadow-2xl border border-opacity-20 overflow-hidden backdrop-blur-lg slide-in">
        <div className={`h-12 bg-gradient-to-r ${theme.gradient} flex items-center justify-center text-white font-bold relative`}>
          <span className="floating mr-2">🤖</span>
          Assistant IA Clade4AI
          <div className="absolute inset-0 bg-white bg-opacity-10 glow-effect"></div>
        </div>
        <div className="h-84 relative">
          <CopilotChat
            instructions="Bonjour ! Je suis l'assistant IA de Clade4AI. Je peux vous aider à changer le thème de l'interface. Essayez de dire 'Change le thème en orange' ou 'Set the theme to green'."
            labels={{
              title: "Assistant Thème",
              initial: "Salut ! Comment puis-je personnaliser votre interface ?",
            }}
          />
        </div>
      </div>

      {/* Particules d'arrière-plan */}
      <div className="fixed inset-0 pointer-events-none overflow-hidden">
        <div className="absolute top-20 left-10 w-2 h-2 bg-blue-400 rounded-full floating opacity-30"></div>
        <div className="absolute top-40 right-20 w-3 h-3 bg-purple-400 rounded-full floating opacity-20" style={{animationDelay: '1s'}}></div>
        <div className="absolute bottom-40 left-20 w-2 h-2 bg-orange-400 rounded-full floating opacity-25" style={{animationDelay: '2s'}}></div>
        <div className="absolute bottom-20 right-40 w-1 h-1 bg-green-400 rounded-full floating opacity-30" style={{animationDelay: '0.5s'}}></div>
      </div>
    </div>
  );
}
