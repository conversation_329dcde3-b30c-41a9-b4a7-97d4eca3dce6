"use client";

import { useCoAgent, useCopilotAction } from "@copilotkit/react-core";
import { CopilotKitCSSProperties, CopilotSidebar } from "@copilotkit/react-ui";
import { useState } from "react";
import ModelSelector, { AIModel, AI_MODELS } from "../../components/ModelSelector";
import ModelStatus from "../../components/ModelStatus";
import useModelManager from "../../hooks/useModelManager";

export default function CopilotKitPage() {
  const [themeColor, setThemeColor] = useState("#6366f1");
  const [showModelSelector, setShowModelSelector] = useState(false);

  // Model management
  const {
    currentModel,
    isConnected,
    lastResponse,
    error,
    isLoading,
    changeModel,
    clearError
  } = useModelManager();

  // 🪁 Frontend Actions: https://docs.copilotkit.ai/guides/frontend-actions
  useCopilotAction({
    name: "setThemeColor",
    parameters: [{
      name: "themeColor",
      description: "The theme color to set. Make sure to pick nice colors.",
      required: true,
    }],
    handler({ themeColor }) {
      setThemeColor(themeColor);
    },
  });

  // Action to change AI model
  useCopilotAction({
    name: "changeAIModel",
    parameters: [{
      name: "modelName",
      description: "The name of the AI model to switch to (e.g., 'GPT-4', 'Claude', 'Llama')",
      required: true,
    }],
    handler({ modelName }) {
      // Find model by name and switch to it
      const model = AI_MODELS.find(m =>
        m.name.toLowerCase().includes(modelName.toLowerCase()) ||
        m.id.toLowerCase().includes(modelName.toLowerCase())
      );
      if (model) {
        changeModel(model);
      }
    },
  });

  return (
    <main style={{ "--copilot-kit-primary-color": themeColor } as CopilotKitCSSProperties}>
      <YourMainContent
        themeColor={themeColor}
        currentModel={currentModel}
        isConnected={isConnected}
        lastResponse={lastResponse}
        error={error}
        isLoading={isLoading}
        onModelChange={changeModel}
        onClearError={clearError}
        showModelSelector={showModelSelector}
        onToggleModelSelector={() => setShowModelSelector(!showModelSelector)}
      />
      <CopilotSidebar
        clickOutsideToClose={false}
        defaultOpen={true}
        labels={{
          title: "Popup Assistant",
          initial: "👋 Hi, there! You're chatting with an agent. This agent comes with a few tools to get you started.\n\nFor example you can try:\n- **Frontend Tools**: \"Set the theme to orange\"\n- **Shared State**: \"Write a proverb about AI\"\n- **Generative UI**: \"Get the weather in SF\"\n- **Model Selection**: \"Change to GPT-4\" or \"Switch to Claude\"\n\nAs you interact with the agent, you'll see the UI update in real-time to reflect the agent's **state**, **tool calls**, and **progress**."
        }}
      />
    </main>
  );
}

// State of the agent, make sure this aligns with your agent's state.
type AgentState = {
  proverbs: string[];
}

interface YourMainContentProps {
  themeColor: string;
  currentModel: AIModel;
  isConnected: boolean;
  lastResponse?: Date;
  error?: string | null;
  isLoading: boolean;
  onModelChange: (model: AIModel) => Promise<void>;
  onClearError: () => void;
  showModelSelector: boolean;
  onToggleModelSelector: () => void;
}

function YourMainContent({
  themeColor,
  currentModel,
  isConnected,
  lastResponse,
  error,
  isLoading,
  onModelChange,
  onClearError,
  showModelSelector,
  onToggleModelSelector
}: YourMainContentProps) {
  // 🪁 Shared State: https://docs.copilotkit.ai/coagents/shared-state
  const {state, setState} = useCoAgent<AgentState>({
    name: "sample_agent",
    initialState: {
      proverbs: [
        "CopilotKit may be new, but its the best thing since sliced bread.",
      ],
    },
  })

  // 🪁 Frontend Actions: https://docs.copilotkit.ai/coagents/frontend-actions
  useCopilotAction({
    name: "addProverb",
    parameters: [{
      name: "proverb",
      description: "The proverb to add. Make it witty, short and concise.",
      required: true,
    }],
    handler: ({ proverb }) => {
      setState({
        ...state,
        proverbs: [...state.proverbs, proverb],
      });
    },
  });

  //🪁 Generative UI: https://docs.copilotkit.ai/coagents/generative-ui
  useCopilotAction({
    name: "getWeather",
    description: "Get the weather for a given location.",
    available: "disabled",
    parameters: [
      { name: "location", type: "string", required: true },
    ],
    render: ({ args }) => {
      return <WeatherCard location={args.location} themeColor={themeColor} />
    },
  });

  return (
    <div
      style={{ backgroundColor: themeColor }}
      className="h-screen w-screen flex justify-center items-center flex-col transition-colors duration-300 p-4"
    >
      {/* Model Selector Panel */}
      <div className="fixed top-4 right-4 z-50">
        <button
          onClick={onToggleModelSelector}
          className="bg-white/20 backdrop-blur-md text-white px-4 py-2 rounded-lg hover:bg-white/30 transition-all flex items-center gap-2"
        >
          <span className="text-xl">{currentModel.icon}</span>
          <span className="font-medium">{currentModel.name}</span>
          <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-400' : 'bg-red-400'} ${isConnected ? 'animate-pulse' : ''}`} />
        </button>

        {showModelSelector && (
          <div className="absolute top-full right-0 mt-2 w-96">
            <div className="bg-white/95 backdrop-blur-md rounded-lg shadow-xl p-4">
              <div className="mb-4">
                <h3 className="text-lg font-semibold text-gray-800 mb-2">Sélection du modèle IA</h3>
                <ModelStatus
                  model={currentModel}
                  isConnected={isConnected}
                  lastResponse={lastResponse}
                  error={error}
                />
              </div>

              <div className="mb-4">
                <ModelSelector
                  onModelChange={onModelChange}
                  currentModel={currentModel.id}
                  disabled={isLoading}
                />
              </div>

              {error && (
                <div className="mb-4">
                  <button
                    onClick={onClearError}
                    className="w-full bg-red-100 hover:bg-red-200 text-red-700 px-3 py-2 rounded text-sm transition-colors"
                  >
                    Effacer l'erreur
                  </button>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Main Content */}
      <div className="bg-white/20 backdrop-blur-md p-8 rounded-2xl shadow-xl max-w-2xl w-full">
        <h1 className="text-4xl font-bold text-white mb-2 text-center">Proverbs</h1>
        <p className="text-gray-200 text-center italic mb-6">
          This is a demonstrative page, but it could be anything you want! 🪁
          <br />
          <span className="text-sm">Modèle actuel: {currentModel.name} {isConnected ? '✅' : '❌'}</span>
        </p>
        <hr className="border-white/20 my-6" />
        <div className="flex flex-col gap-3">
          {state.proverbs?.map((proverb, index) => (
            <div
              key={index}
              className="bg-white/15 p-4 rounded-xl text-white relative group hover:bg-white/20 transition-all"
            >
              <p className="pr-8">{proverb}</p>
              <button
                onClick={() => setState({
                  ...state,
                  proverbs: state.proverbs?.filter((_, i) => i !== index),
                })}
                className="absolute right-3 top-3 opacity-0 group-hover:opacity-100 transition-opacity
                  bg-red-500 hover:bg-red-600 text-white rounded-full h-6 w-6 flex items-center justify-center"
              >
                ✕
              </button>
            </div>
          ))}
        </div>
        {state.proverbs?.length === 0 && <p className="text-center text-white/80 italic my-8">
          No proverbs yet. Ask the assistant to add some!
        </p>}
      </div>
    </div>
  );
}

// Simple sun icon for the weather card
function SunIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-14 h-14 text-yellow-200">
      <circle cx="12" cy="12" r="5" />
      <path d="M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42" strokeWidth="2" stroke="currentColor" />
    </svg>
  );
}

// Weather card component where the location and themeColor are based on what the agent
// sets via tool calls.
function WeatherCard({ location, themeColor }: { location?: string, themeColor: string }) {
  return (
    <div
    style={{ backgroundColor: themeColor }}
    className="rounded-xl shadow-xl mt-6 mb-4 max-w-md w-full"
  >
    <div className="bg-white/20 p-4 w-full">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-xl font-bold text-white capitalize">{location}</h3>
          <p className="text-white">Current Weather</p>
        </div>
        <SunIcon />
      </div>
      
      <div className="mt-4 flex items-end justify-between">
        <div className="text-3xl font-bold text-white">70°</div>
        <div className="text-sm text-white">Clear skies</div>
      </div>
      
      <div className="mt-4 pt-4 border-t border-white">
        <div className="grid grid-cols-3 gap-2 text-center">
          <div>
            <p className="text-white text-xs">Humidity</p>
            <p className="text-white font-medium">45%</p>
          </div>
          <div>
            <p className="text-white text-xs">Wind</p>
            <p className="text-white font-medium">5 mph</p>
          </div>
          <div>
            <p className="text-white text-xs">Feels Like</p>
            <p className="text-white font-medium">72°</p>
          </div>
        </div>
      </div>
    </div>
  </div>
  );
}
