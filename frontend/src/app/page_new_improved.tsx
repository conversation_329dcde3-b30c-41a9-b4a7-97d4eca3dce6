"use client";

import { useState } from "react";
import { CopilotChat } from "@copilotkit/react-ui";

type Theme = "blue" | "orange" | "green" | "purple" | "red" | "pink" | "dark" | "light";

const themes = {
  blue: {
    primary: "bg-blue-600",
    secondary: "bg-blue-50", 
    text: "text-blue-600",
    border: "border-blue-600",
    gradient: "from-blue-400 via-blue-500 to-blue-600",
    accent: "bg-blue-500",
    hover: "hover:bg-blue-700"
  },
  orange: {
    primary: "bg-orange-600",
    secondary: "bg-orange-50",
    text: "text-orange-600", 
    border: "border-orange-600",
    gradient: "from-orange-400 via-orange-500 to-orange-600",
    accent: "bg-orange-500",
    hover: "hover:bg-orange-700"
  },
  green: {
    primary: "bg-green-600",
    secondary: "bg-green-50",
    text: "text-green-600",
    border: "border-green-600", 
    gradient: "from-green-400 via-green-500 to-green-600",
    accent: "bg-green-500",
    hover: "hover:bg-green-700"
  },
  purple: {
    primary: "bg-purple-600",
    secondary: "bg-purple-50",
    text: "text-purple-600",
    border: "border-purple-600",
    gradient: "from-purple-400 via-purple-500 to-purple-600",
    accent: "bg-purple-500",
    hover: "hover:bg-purple-700"
  },
  red: {
    primary: "bg-red-600", 
    secondary: "bg-red-50",
    text: "text-red-600",
    border: "border-red-600",
    gradient: "from-red-400 via-red-500 to-red-600",
    accent: "bg-red-500",
    hover: "hover:bg-red-700"
  },
  pink: {
    primary: "bg-pink-600",
    secondary: "bg-pink-50",
    text: "text-pink-600",
    border: "border-pink-600", 
    gradient: "from-pink-400 via-pink-500 to-pink-600",
    accent: "bg-pink-500",
    hover: "hover:bg-pink-700"
  },
  dark: {
    primary: "bg-gray-800",
    secondary: "bg-gray-900",
    text: "text-gray-100",
    border: "border-gray-600",
    gradient: "from-gray-700 via-gray-800 to-gray-900",
    accent: "bg-gray-700",
    hover: "hover:bg-gray-900"
  },
  light: {
    primary: "bg-gray-100",
    secondary: "bg-gray-50",
    text: "text-gray-800",
    border: "border-gray-300",
    gradient: "from-gray-50 via-gray-100 to-gray-200",
    accent: "bg-gray-200",
    hover: "hover:bg-gray-300"
  }
};

export default function Home() {
  const [currentTheme, setCurrentTheme] = useState<Theme>("blue");
  const [message, setMessage] = useState("");
  const [isAnimating, setIsAnimating] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(false);

  const theme = themes[currentTheme];

  const handleThemeChange = (newTheme: Theme) => {
    setIsAnimating(true);
    setCurrentTheme(newTheme);
    setMessage(`Thème changé vers ${newTheme} !`);
    setTimeout(() => {
      setIsAnimating(false);
      setMessage("");
    }, 2000);
  };

  return (
    <div className={`min-h-screen transition-all duration-700 ${isDarkMode ? 'bg-gray-900' : 'bg-gray-50'}`}>
      {/* Header moderne avec animation */}
      <header className={`bg-gradient-to-r ${theme.gradient} text-white relative overflow-hidden`}>
        <div className="absolute inset-0 bg-black opacity-10"></div>
        <div className="relative max-w-6xl mx-auto px-6 py-12">
          <div className="flex justify-between items-center mb-6">
            <div>
              <h1 className="text-5xl font-bold mb-3 bg-white bg-opacity-20 backdrop-blur-sm inline-block px-4 py-2 rounded-lg">
                🚁 Clade4AI Demo
              </h1>
              <p className="text-xl opacity-90 mt-2">
                Interface intelligente avec assistant IA intégré
              </p>
            </div>
            <button
              onClick={() => setIsDarkMode(!isDarkMode)}
              className="bg-white bg-opacity-20 hover:bg-opacity-30 backdrop-blur-sm rounded-full p-3 transition-all duration-300"
            >
              {isDarkMode ? "☀️" : "🌙"}
            </button>
          </div>
          
          {/* Indicateurs de statut */}
          <div className="flex gap-4 text-sm">
            <div className="bg-green-500 bg-opacity-20 backdrop-blur-sm rounded-full px-3 py-1 flex items-center gap-2">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              Assistant IA actif
            </div>
            <div className="bg-blue-500 bg-opacity-20 backdrop-blur-sm rounded-full px-3 py-1 flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
              Thème: {currentTheme}
            </div>
          </div>
        </div>
        
        {/* Formes géométriques animées */}
        <div className="absolute top-10 right-10 w-20 h-20 bg-white bg-opacity-10 rounded-full animate-bounce"></div>
        <div className="absolute bottom-10 left-10 w-16 h-16 bg-white bg-opacity-10 rotate-45 animate-pulse"></div>
      </header>

      {/* Message de confirmation avec animation */}
      {message && (
        <div className={`${theme.secondary} ${theme.text} p-4 text-center font-medium transform transition-all duration-500`}>
          <div className="flex items-center justify-center gap-2">
            <span className="animate-spin">✨</span>
            {message}
            <span className="animate-pulse">🎨</span>
          </div>
        </div>
      )}

      {/* Contenu principal avec layout amélioré */}
      <main className={`max-w-6xl mx-auto p-6 ${isAnimating ? 'animate-pulse' : ''}`}>
        
        {/* Section héro modernisée */}
        <div className={`${theme.secondary} rounded-2xl p-8 mb-8 shadow-lg border-l-4 ${theme.border} relative overflow-hidden`}>
          <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-transparent to-white opacity-5 rounded-full"></div>
          <div className="relative z-10">
            <h2 className={`${theme.text} text-3xl font-bold mb-4 flex items-center gap-3`}>
              <span className="text-4xl">🎨</span>
              Contrôle de thème intelligent
            </h2>
            <p className={`text-gray-700 mb-6 text-lg ${isDarkMode ? 'text-gray-300' : ''}`}>
              Utilisez l&apos;assistant IA pour personnaliser l&apos;interface en temps réel
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className={`bg-white ${isDarkMode ? 'bg-gray-800' : ''} rounded-xl p-6 shadow-md`}>
                <h3 className={`${theme.text} text-xl font-bold mb-4 flex items-center gap-2`}>
                  🗣️ Commandes vocales
                </h3>
                <ul className={`space-y-3 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  <li className="flex items-center gap-2">
                    <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                    &quot;Change le thème en orange&quot;
                  </li>
                  <li className="flex items-center gap-2">
                    <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                    &quot;Set the theme to purple&quot;
                  </li>
                  <li className="flex items-center gap-2">
                    <span className="w-2 h-2 bg-red-500 rounded-full"></span>
                    &quot;Utilise le thème sombre&quot;
                  </li>
                </ul>
              </div>
              
              <div className={`bg-white ${isDarkMode ? 'bg-gray-800' : ''} rounded-xl p-6 shadow-md`}>
                <h3 className={`${theme.text} text-xl font-bold mb-4 flex items-center gap-2`}>
                  ⚡ Actions rapides
                </h3>
                <div className="space-y-3">
                  <button
                    onClick={() => handleThemeChange("orange")}
                    className="w-full bg-orange-500 hover:bg-orange-600 text-white py-2 px-4 rounded-lg transition-all duration-300 transform hover:scale-105"
                  >
                    Mode énergique 🔥
                  </button>
                  <button
                    onClick={() => handleThemeChange("dark")}
                    className="w-full bg-gray-800 hover:bg-gray-900 text-white py-2 px-4 rounded-lg transition-all duration-300 transform hover:scale-105"
                  >
                    Mode nocturne 🌙
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Grille des thèmes interactive */}
        <div className="mb-8">
          <h2 className={`text-3xl font-bold mb-6 ${isDarkMode ? 'text-white' : 'text-gray-800'} flex items-center gap-3`}>
            <span className="text-4xl">🎨</span>
            Palette de thèmes
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
            {Object.entries(themes).map(([name, themeConfig]) => (
              <button
                key={name}
                onClick={() => handleThemeChange(name as Theme)}
                className={`
                  group relative ${themeConfig.primary} ${themeConfig.hover}
                  text-white p-6 rounded-xl transition-all duration-300
                  transform hover:scale-110 hover:shadow-xl
                  ${currentTheme === name ? 'ring-4 ring-yellow-400 scale-105' : ''}
                  ${isAnimating ? 'animate-pulse' : ''}
                `}
              >
                <div className="text-center">
                  <div className="text-2xl mb-2">
                    {name === 'dark' ? '🌙' : name === 'light' ? '☀️' : '🎨'}
                  </div>
                  <div className="text-sm font-semibold capitalize mb-1">{name}</div>
                  <div className="text-xs opacity-70 group-hover:opacity-100 transition-opacity">
                    Cliquer
                  </div>
                </div>
                
                {/* Effet de brillance au survol */}
                <div className="absolute inset-0 bg-white opacity-0 group-hover:opacity-20 rounded-xl transition-opacity duration-300"></div>
                
                {/* Indicateur thème actuel */}
                {currentTheme === name && (
                  <div className="absolute -top-2 -right-2 w-6 h-6 bg-yellow-400 rounded-full flex items-center justify-center">
                    <span className="text-xs">✓</span>
                  </div>
                )}
              </button>
            ))}
          </div>
        </div>

        {/* Cards d'information avec design moderne */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {/* Card Fonctionnalités */}
          <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-2xl shadow-xl p-6 transform hover:scale-105 transition-all duration-300 border-l-4 ${theme.border}`}>
            <div className="flex items-center gap-3 mb-4">
              <div className={`w-12 h-12 ${theme.primary} rounded-full flex items-center justify-center text-white text-xl`}>
                🚀
              </div>
              <h3 className={`text-xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
                Fonctionnalités IA
              </h3>
            </div>
            <ul className={`space-y-3 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
              <li className="flex items-center gap-2">
                <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                Chat IA interactif
              </li>
              <li className="flex items-center gap-2">
                <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                Commandes vocales
              </li>
              <li className="flex items-center gap-2">
                <span className="w-2 h-2 bg-purple-500 rounded-full"></span>
                Changement dynamique
              </li>
              <li className="flex items-center gap-2">
                <span className="w-2 h-2 bg-orange-500 rounded-full"></span>
                Intégration LangGraph
              </li>
            </ul>
          </div>

          {/* Card Architecture */}
          <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-2xl shadow-xl p-6 transform hover:scale-105 transition-all duration-300 border-l-4 ${theme.border}`}>
            <div className="flex items-center gap-3 mb-4">
              <div className={`w-12 h-12 ${theme.primary} rounded-full flex items-center justify-center text-white text-xl`}>
                ⚡
              </div>
              <h3 className={`text-xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
                Technologies
              </h3>
            </div>
            <ul className={`space-y-3 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
              <li className="flex items-center gap-2">
                <span className="w-2 h-2 bg-cyan-500 rounded-full"></span>
                Next.js 15 + Turbopack
              </li>
              <li className="flex items-center gap-2">
                <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                CopilotKit React UI
              </li>
              <li className="flex items-center gap-2">
                <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                Agent LangGraph
              </li>
              <li className="flex items-center gap-2">
                <span className="w-2 h-2 bg-pink-500 rounded-full"></span>
                Tailwind CSS
              </li>
            </ul>
          </div>

          {/* Card Instructions */}
          <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-2xl shadow-xl p-6 transform hover:scale-105 transition-all duration-300 border-l-4 ${theme.border} md:col-span-2 lg:col-span-1`}>
            <div className="flex items-center gap-3 mb-4">
              <div className={`w-12 h-12 ${theme.primary} rounded-full flex items-center justify-center text-white text-xl`}>
                💡
              </div>
              <h3 className={`text-xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
                Guide rapide
              </h3>
            </div>
            <ol className={`space-y-3 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
              <li className="flex items-start gap-3">
                <span className={`w-6 h-6 ${theme.primary} text-white rounded-full flex items-center justify-center text-xs font-bold`}>1</span>
                <span>Ouvrez l&apos;assistant en bas à droite</span>
              </li>
              <li className="flex items-start gap-3">
                <span className={`w-6 h-6 ${theme.primary} text-white rounded-full flex items-center justify-center text-xs font-bold`}>2</span>
                <span>Dites: &quot;Change le thème en orange&quot;</span>
              </li>
              <li className="flex items-start gap-3">
                <span className={`w-6 h-6 ${theme.primary} text-white rounded-full flex items-center justify-center text-xs font-bold`}>3</span>
                <span>Regardez la magie opérer ! ✨</span>
              </li>
            </ol>
          </div>
        </div>

        {/* Section statistiques/métriques */}
        <div className={`${theme.secondary} rounded-2xl p-8 mb-8 shadow-lg`}>
          <h3 className={`${theme.text} text-2xl font-bold mb-6 text-center`}>
            🎯 Performances en temps réel
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className={`${theme.text} text-3xl font-bold`}>8</div>
              <div className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'} text-sm`}>Thèmes disponibles</div>
            </div>
            <div className="text-center">
              <div className={`${theme.text} text-3xl font-bold`}>100%</div>
              <div className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'} text-sm`}>Réactif</div>
            </div>
            <div className="text-center">
              <div className={`${theme.text} text-3xl font-bold`}>⚡</div>
              <div className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'} text-sm`}>Instantané</div>
            </div>
            <div className="text-center">
              <div className={`${theme.text} text-3xl font-bold`}>🤖</div>
              <div className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'} text-sm`}>IA intégrée</div>
            </div>
          </div>
        </div>
      </main>

      {/* Assistant CopilotKit amélioré */}
      <div className="fixed bottom-4 right-4 w-96 h-96 bg-white rounded-2xl shadow-2xl border border-gray-200 overflow-hidden">
        <div className={`h-12 bg-gradient-to-r ${theme.gradient} flex items-center justify-center text-white font-bold`}>
          🤖 Assistant IA Clade4AI
        </div>
        <div className="h-84">
          <CopilotChat
            instructions="Bonjour ! Je suis l'assistant IA de Clade4AI. Je peux vous aider à changer le thème de l'interface. Essayez de dire 'Change le thème en orange' ou 'Set the theme to green'."
            labels={{
              title: "Assistant Thème",
              initial: "Salut ! Comment puis-je personnaliser votre interface ?",
            }}
          />
        </div>
      </div>
    </div>
  );
}
