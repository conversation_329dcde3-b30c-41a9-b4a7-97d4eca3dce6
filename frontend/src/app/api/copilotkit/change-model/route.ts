import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { modelId } = await request.json();

    if (!modelId) {
      return NextResponse.json(
        { error: 'Model ID is required' },
        { status: 400 }
      );
    }

    // Store the selected model in a way that can be accessed by the main route
    // We'll use headers to pass this information to the LangGraph agent
    const response = NextResponse.json({ 
      success: true, 
      message: `Model changed to ${modelId}`,
      modelId 
    });

    // Set a cookie to persist the model selection
    response.cookies.set('copilotkit-model', modelId, {
      httpOnly: false,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 60 * 24 * 30 // 30 days
    });

    return response;
  } catch (error) {
    console.error('Error changing model:', error);
    return NextResponse.json(
      { error: 'Failed to change model' },
      { status: 500 }
    );
  }
}
