import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { model, message } = await request.json();

    if (!model) {
      return NextResponse.json(
        { error: 'Model is required' },
        { status: 400 }
      );
    }

    // Test the model by sending a simple request to the LangGraph agent
    const langGraphUrl = process.env.LANGGRAPH_DEPLOYMENT_URL || 'http://127.0.0.1:8123';
    
    const testPayload = {
      input: {
        messages: [
          {
            role: 'user',
            content: message || 'Test connection'
          }
        ],
        copilotkit: {
          actions: []
        }
      },
      config: {
        configurable: {
          model_id: model
        }
      }
    };

    const response = await fetch(`${langGraphUrl}/runs/stream`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testPayload),
    });

    if (response.ok) {
      return NextResponse.json({ 
        success: true, 
        message: 'Model connection successful',
        model 
      });
    } else {
      const errorText = await response.text();
      return NextResponse.json(
        { error: `Model test failed: ${errorText}` },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error testing model:', error);
    return NextResponse.json(
      { error: 'Failed to test model connection' },
      { status: 500 }
    );
  }
}
