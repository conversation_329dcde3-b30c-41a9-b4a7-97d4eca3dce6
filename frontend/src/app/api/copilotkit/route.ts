import { NextRequest } from "next/server";
import {
  CopilotRuntime,
  copilotRuntimeNextJSAppRouterEndpoint,
  ExperimentalEmptyAdapter,
  langGraphPlatformEndpoint,
} from "@copilotkit/runtime";

const serviceAdapter = new ExperimentalEmptyAdapter();

// Function to create runtime with dynamic model support
function createRuntime(request: NextRequest) {
  // Get selected model from cookie or header
  const selectedModel = request.cookies.get('copilotkit-model')?.value ||
                       request.headers.get('x-copilotkit-model') ||
                       'openai/gpt-3.5-turbo'; // default

  // Store the selected model in a global variable that the agent can access
  (global as any).selectedModel = selectedModel;

  return new CopilotRuntime({
    remoteEndpoints: [
      langGraphPlatformEndpoint({
        deploymentUrl: process.env.LANGGRAPH_DEPLOYMENT_URL || "",
        langsmithApiKey: process.env.LANGSMITH_API_KEY || "",
        agents: [{
          name: process.env.NEXT_PUBLIC_COPILOTKIT_AGENT_NAME || "",
          description: process.env.NEXT_PUBLIC_COPILOTKIT_AGENT_DESCRIPTION || 'A helpful LLM agent.'
        }]
      }),
    ],
  });
}

export const POST = async (req: NextRequest) => {
  // Create runtime with dynamic model selection
  const runtime = createRuntime(req);

  const { handleRequest } = copilotRuntimeNextJSAppRouterEndpoint({
    runtime,
    serviceAdapter,
    endpoint: "/api/copilotkit",
  });

  return handleRequest(req);
};
