import React from 'react';

interface ThemeStyles {
  primary: string;
  secondary: string;
  text: string;
  border: string;
  gradient: string;
  accent: string;
  hover: string;
}

interface ThemeCardProps {
  name: string;
  config: ThemeStyles;
  isActive: boolean;
  isAnimating: boolean;
  onClick: () => void;
}

export const ThemeCard: React.FC<ThemeCardProps> = ({
  name,
  config,
  isActive,
  isAnimating,
  onClick
}) => {
  const getThemeIcon = (themeName: string) => {
    switch (themeName) {
      case 'dark': return '🌙';
      case 'light': return '☀️';
      case 'orange': return '🔥';
      case 'green': return '🌿';
      case 'blue': return '💧';
      case 'purple': return '🔮';
      case 'red': return '❤️';
      case 'pink': return '🌸';
      default: return '🎨';
    }
  };

  return (
    <button
      onClick={onClick}
      className={`
        group relative ${config.primary} ${config.hover}
        text-white p-6 rounded-xl transition-all duration-300
        transform hover:scale-110 hover:shadow-xl
        ${isActive ? 'ring-4 ring-yellow-400 scale-105' : ''}
        ${isAnimating ? 'animate-pulse' : ''}
        hover-lift
      `}
    >
      <div className="text-center relative z-10">
        <div className="text-3xl mb-2 animate-float">
          {getThemeIcon(name)}
        </div>
        <div className="text-sm font-semibold capitalize mb-1">{name}</div>
        <div className="text-xs opacity-70 group-hover:opacity-100 transition-opacity">
          Cliquer
        </div>
      </div>
      
      {/* Effet de brillance au survol */}
      <div className="absolute inset-0 bg-white opacity-0 group-hover:opacity-20 rounded-xl transition-opacity duration-300 btn-primary"></div>
      
      {/* Indicateur thème actuel */}
      {isActive && (
        <div className="absolute -top-2 -right-2 w-6 h-6 bg-yellow-400 rounded-full flex items-center justify-center animate-scale-in">
          <span className="text-xs text-black font-bold">✓</span>
        </div>
      )}
      
      {/* Particules animées pour le thème actif */}
      {isActive && (
        <>
          <div className="absolute top-2 left-2 w-1 h-1 bg-yellow-300 rounded-full animate-ping"></div>
          <div className="absolute bottom-2 right-2 w-1 h-1 bg-yellow-300 rounded-full animate-ping" style={{animationDelay: '0.5s'}}></div>
        </>
      )}
    </button>
  );
};

interface FeatureCardProps {
  icon: string;
  title: string;
  description: string;
  features: string[];
  theme: ThemeStyles;
  isDarkMode: boolean;
}

export const FeatureCard: React.FC<FeatureCardProps> = ({
  icon,
  title,
  description,
  features,
  theme,
  isDarkMode
}) => {
  return (
    <div className={`
      ${isDarkMode ? 'bg-gray-800 glass-dark' : 'bg-white glass'} 
      rounded-2xl shadow-xl p-6 transform hover:scale-105 transition-all duration-300 
      border-l-4 ${theme.border} hover-lift animate-slide-in-top
    `}>
      <div className="flex items-center gap-3 mb-4">
        <div className={`w-12 h-12 ${theme.primary} rounded-full flex items-center justify-center text-white text-xl animate-glow`}>
          {icon}
        </div>
        <h3 className={`text-xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-800'} text-gradient`}>
          {title}
        </h3>
      </div>
      <p className={`${isDarkMode ? 'text-gray-300' : 'text-gray-600'} mb-4 text-sm`}>
        {description}
      </p>
      <ul className={`space-y-3 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
        {features.map((feature, index) => (
          <li key={index} className="flex items-center gap-2 animate-slide-in-right" style={{animationDelay: `${index * 0.1}s`}}>
            <span className={`w-2 h-2 ${theme.primary} rounded-full status-indicator`}></span>
            {feature}
          </li>
        ))}
      </ul>
    </div>
  );
};

interface QuickActionButtonProps {
  label: string;
  icon: string;
  onClick: () => void;
  variant: 'primary' | 'secondary';
  className?: string;
}

export const QuickActionButton: React.FC<QuickActionButtonProps> = ({
  label,
  icon,
  onClick,
  variant,
  className = ''
}) => {
  const baseClasses = "w-full py-3 px-4 rounded-lg transition-all duration-300 transform hover:scale-105 btn-primary font-semibold";
  const variantClasses = variant === 'primary' 
    ? "bg-orange-500 hover:bg-orange-600 text-white" 
    : "bg-gray-800 hover:bg-gray-900 text-white";

  return (
    <button
      onClick={onClick}
      className={`${baseClasses} ${variantClasses} ${className}`}
    >
      {label} {icon}
    </button>
  );
};

interface StatusIndicatorProps {
  label: string;
  status: 'active' | 'inactive';
  info: string;
}

export const StatusIndicator: React.FC<StatusIndicatorProps> = ({
  label,
  status,
  info
}) => {
  const statusColor = status === 'active' ? 'bg-green-500' : 'bg-gray-500';
  
  return (
    <div className="bg-white bg-opacity-20 backdrop-blur-sm rounded-full px-4 py-2 flex items-center gap-2 glass">
      <div className={`w-2 h-2 ${statusColor} rounded-full ${status === 'active' ? 'animate-pulse' : ''} status-indicator`}></div>
      <span className="text-sm font-medium">{label}</span>
      <span className="text-xs opacity-80">({info})</span>
    </div>
  );
};
