import React from 'react';
import { StatusIndicator } from './theme-components';

interface ThemeStyles {
  primary: string;
  secondary: string;
  text: string;
  border: string;
  gradient: string;
  accent: string;
  hover: string;
}

interface HeaderProps {
  theme: ThemeStyles;
  isDarkMode: boolean;
  currentTheme: string;
  onToggleDarkMode: () => void;
}

export const ModernHeader: React.FC<HeaderProps> = ({
  theme,
  isDarkMode,
  currentTheme,
  onToggleDarkMode
}) => {
  return (
    <header className={`bg-gradient-to-r ${theme.gradient} text-white relative overflow-hidden`}>
      {/* Overlay pour la profondeur */}
      <div className="absolute inset-0 bg-black opacity-10"></div>
      
      {/* Particules de fond animées */}
      <div className="absolute inset-0">
        {Array.from({ length: 20 }).map((_, i) => (
          <div
            key={i}
            className="absolute w-2 h-2 bg-white opacity-20 rounded-full animate-float"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${3 + Math.random() * 2}s`
            }}
          />
        ))}
      </div>
      
      <div className="relative max-w-6xl mx-auto px-6 py-12">
        <div className="flex justify-between items-center mb-6">
          <div className="animate-slide-in-right">
            <h1 className="text-6xl font-bold mb-3 bg-white bg-opacity-20 backdrop-blur-sm inline-block px-6 py-3 rounded-xl glass">
              🚁 Clade4AI Demo
            </h1>
            <p className="text-xl opacity-90 mt-4 animate-slide-in-right" style={{animationDelay: '0.2s'}}>
              Interface intelligente avec assistant IA de nouvelle génération
            </p>
            <div className="mt-4 animate-slide-in-right" style={{animationDelay: '0.4s'}}>
              <span className="bg-yellow-400 text-black px-3 py-1 rounded-full text-sm font-bold">
                🆕 Nouvelle interface 2025
              </span>
            </div>
          </div>
          
          <button
            onClick={onToggleDarkMode}
            className="bg-white bg-opacity-20 hover:bg-opacity-30 backdrop-blur-sm rounded-full p-4 transition-all duration-300 glass hover-lift animate-slide-in-left"
          >
            <span className="text-2xl">{isDarkMode ? "☀️" : "🌙"}</span>
          </button>
        </div>
        
        {/* Indicateurs de statut améliorés */}
        <div className="flex flex-wrap gap-4 text-sm">
          <StatusIndicator 
            label="Assistant IA"
            status="active"
            info="Connecté"
          />
          <StatusIndicator 
            label="Thème actuel"
            status="active"
            info={currentTheme}
          />
          <div className="bg-blue-500 bg-opacity-20 backdrop-blur-sm rounded-full px-4 py-2 flex items-center gap-2 glass">
            <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium">LangGraph Agent</span>
          </div>
        </div>
        
        {/* Mini métriques rapides */}
        <div className="flex gap-4 mt-6">
          <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-lg p-3 text-center glass">
            <div className="text-2xl font-bold">8</div>
            <div className="text-xs opacity-80">Thèmes</div>
          </div>
          <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-lg p-3 text-center glass">
            <div className="text-2xl font-bold">100%</div>
            <div className="text-xs opacity-80">Réactif</div>
          </div>
          <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-lg p-3 text-center glass">
            <div className="text-2xl font-bold">⚡</div>
            <div className="text-xs opacity-80">Rapide</div>
          </div>
          <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-lg p-3 text-center glass">
            <div className="text-2xl font-bold">🤖</div>
            <div className="text-xs opacity-80">IA</div>
          </div>
        </div>
      </div>
      
      {/* Formes géométriques animées améliorées */}
      <div className="absolute top-10 right-10 w-20 h-20 bg-white bg-opacity-10 rounded-full animate-bounce"></div>
      <div className="absolute bottom-10 left-10 w-16 h-16 bg-white bg-opacity-10 rotate-45 animate-pulse"></div>
      <div className="absolute top-1/2 left-1/4 w-12 h-12 bg-white bg-opacity-5 rounded-full animate-float"></div>
      
      {/* Gradient décoratif en bas */}
      <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white to-transparent opacity-30"></div>
    </header>
  );
};

interface NotificationProps {
  message: string;
  theme: ThemeStyles;
  onClose?: () => void;
}

export const Notification: React.FC<NotificationProps> = ({
  message,
  theme,
  onClose
}) => {
  return (
    <div className={`${theme.secondary} ${theme.text} p-4 text-center font-medium transform transition-all duration-500 animate-slide-in-top relative overflow-hidden`}>
      {/* Barre de progression */}
      <div 
        className={`absolute bottom-0 left-0 h-1 ${theme.primary}`}
        style={{
          width: '100%',
          animation: 'shrink 2s linear forwards'
        }}
      ></div>
      
      <div className="flex items-center justify-center gap-3">
        <span className="animate-spin text-xl">✨</span>
        <span className="font-semibold">{message}</span>
        <span className="animate-pulse text-xl">🎨</span>
        
        {onClose && (
          <button 
            onClick={onClose}
            className="ml-4 text-gray-500 hover:text-gray-700 transition-colors"
          >
            ✕
          </button>
        )}
      </div>
    </div>
  );
};

interface PerformanceMetricsProps {
  theme: ThemeStyles;
  isDarkMode: boolean;
}

export const PerformanceMetrics: React.FC<PerformanceMetricsProps> = ({
  theme,
  isDarkMode
}) => {
  const metrics = [
    { label: "Thèmes", value: "8", icon: "🎨" },
    { label: "Réactivité", value: "100%", icon: "⚡" },
    { label: "Performance", value: "A+", icon: "🚀" },
    { label: "IA", value: "Actif", icon: "🤖" }
  ];

  return (
    <div className={`${theme.secondary} rounded-2xl p-8 mb-8 shadow-lg animate-scale-in glass-card`}>
      <h3 className={`${theme.text} text-2xl font-bold mb-6 text-center flex items-center justify-center gap-2`}>
        <span className="floating">🎯</span> Métriques en temps réel
      </h3>
      <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
        {metrics.map((metric, index) => (
          <div 
            key={metric.label}
            className="text-center animate-slide-in-top hover-lift" 
            style={{animationDelay: `${index * 0.1}s`}}
          >
            <div className={`${theme.text} text-4xl font-bold mb-2 flex items-center justify-center gap-2`}>
              <span className="floating">{metric.icon}</span>
              {metric.value}
            </div>
            <div className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'} text-sm`}>
              {metric.label}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
