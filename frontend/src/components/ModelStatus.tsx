"use client";

import React from 'react';
import { AIModel } from './ModelSelector';

interface ModelStatusProps {
  model: AIModel;
  isConnected: boolean;
  lastResponse?: Date;
  error?: string | null;
}

export const ModelStatus: React.FC<ModelStatusProps> = ({
  model,
  isConnected,
  lastResponse,
  error
}) => {
  const getStatusColor = () => {
    if (error) return 'bg-red-500';
    if (isConnected) return 'bg-green-500';
    return 'bg-yellow-500';
  };

  const getStatusText = () => {
    if (error) return 'Erreur';
    if (isConnected) return 'Connecté';
    return 'Connexion...';
  };

  const getPricingBadgeColor = (pricing: string) => {
    switch (pricing) {
      case 'free': return 'bg-green-100 text-green-800 border-green-200';
      case 'cheap': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'moderate': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'premium': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getPricingLabel = (pricing: string) => {
    switch (pricing) {
      case 'free': return 'Gratuit';
      case 'cheap': return 'Économique';
      case 'moderate': return 'Modéré';
      case 'premium': return 'Premium';
      default: return 'Inconnu';
    }
  };

  const formatLastResponse = (date?: Date) => {
    if (!date) return 'Jamais';
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (seconds < 60) return 'À l\'instant';
    if (minutes < 60) return `Il y a ${minutes}m`;
    if (hours < 24) return `Il y a ${hours}h`;
    return date.toLocaleDateString();
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
      {/* Header with model info */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-3">
          <span className="text-2xl">{model.icon}</span>
          <div>
            <h3 className="font-semibold text-gray-900">{model.name}</h3>
            <p className="text-sm text-gray-600">{model.provider}</p>
          </div>
        </div>
        
        {/* Status indicator */}
        <div className="flex items-center gap-2">
          <div className={`w-3 h-3 rounded-full ${getStatusColor()} ${isConnected ? 'animate-pulse' : ''}`} />
          <span className="text-sm font-medium text-gray-700">{getStatusText()}</span>
        </div>
      </div>

      {/* Model details */}
      <div className="space-y-2">
        <div className="flex flex-wrap gap-2">
          {/* Pricing badge */}
          <span className={`px-2 py-1 text-xs font-medium rounded-full border ${getPricingBadgeColor(model.pricing)}`}>
            {getPricingLabel(model.pricing)}
          </span>
          
          {/* Performance badge */}
          <span className="px-2 py-1 text-xs font-medium rounded-full border bg-gray-100 text-gray-800 border-gray-200">
            Performance: {model.performance}
          </span>
          
          {/* Tools support */}
          {model.supportsTools && (
            <span className="px-2 py-1 text-xs font-medium rounded-full border bg-green-100 text-green-800 border-green-200">
              🛠️ Outils supportés
            </span>
          )}
        </div>

        {/* Context window */}
        <div className="text-sm text-gray-600">
          <span className="font-medium">Contexte:</span> {model.contextWindow}
        </div>

        {/* Description */}
        <p className="text-sm text-gray-600">{model.description}</p>

        {/* Last response time */}
        <div className="text-xs text-gray-500 pt-2 border-t border-gray-100">
          <span className="font-medium">Dernière réponse:</span> {formatLastResponse(lastResponse)}
        </div>

        {/* Error display */}
        {error && (
          <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
            <span className="font-medium">Erreur:</span> {error}
          </div>
        )}
      </div>
    </div>
  );
};

export default ModelStatus;
