// Types pour le système de thèmes Clade4AI

export interface ThemeConfig {
  primary: string;
  secondary: string;
  text: string;
  border: string;
  gradient: string;
  accent: string;
  hover: string;
}

export type Theme = "blue" | "orange" | "green" | "purple" | "red" | "pink" | "dark" | "light";

export interface UseThemeReturn {
  currentTheme: Theme;
  theme: ThemeConfig;
  isDarkMode: boolean;
  isAnimating: boolean;
  message: string;
  handleThemeChange: (newTheme: Theme) => void;
  toggleDarkMode: () => void;
  setMessage: (message: string) => void;
}
