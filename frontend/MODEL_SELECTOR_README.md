# 🤖 Sélecteur de Modèle IA Dynamique - CopilotKit

Cette fonctionnalité permet aux utilisateurs de changer dynamiquement le modèle d'IA utilisé par l'assistant CopilotKit sans redémarrer l'application.

## 🎯 Fonctionnalités

### ✅ Implémentées
- **Sélection dynamique** : Changement de modèle en temps réel
- **Interface utilisateur** : Dropdown avec informations détaillées sur chaque modèle
- **Persistance** : Sauvegarde du choix dans localStorage
- **Indicateurs visuels** : Statut de connexion, performance, coût
- **Gestion d'erreurs** : Affichage et gestion des erreurs de modèle
- **Actions vocales** : Commandes comme "Change to GPT-4" ou "Switch to Claude"

### 📋 Modèles Supportés

| Modèle | Provider | Coût | Performance | Outils | Contexte |
|--------|----------|------|-------------|--------|----------|
| 🦙 Llama 3.2 3B | Meta | Gratuit | Basic | ❌ | 128K |
| 🤖 GPT-3.5 Turbo | OpenAI | Économique | Good | ✅ | 16K |
| 🧠 GPT-4 | OpenAI | Premium | Excellent | ✅ | 128K |
| 🎭 Claude 3 Haiku | Anthropic | Économique | Good | ✅ | 200K |
| 🎨 Claude 3.5 Sonnet | Anthropic | Premium | Premium | ✅ | 200K |
| 💎 Gemini Pro | Google | Modéré | Excellent | ✅ | 32K |

## 🚀 Utilisation

### Interface Graphique
1. Cliquez sur le bouton du modèle actuel (coin supérieur droit)
2. Sélectionnez un nouveau modèle dans la liste déroulante
3. Le changement est immédiat et persistant

### Commandes Vocales
- "Change to GPT-4"
- "Switch to Claude"
- "Use the free model"
- "Change to Llama"

### Programmatique
```typescript
import { useModelManager } from '../hooks/useModelManager';

const { currentModel, changeModel } = useModelManager();

// Changer de modèle
await changeModel(AI_MODELS.find(m => m.id === 'openai/gpt-4'));
```

## 🔧 Architecture Technique

### Composants
- **ModelSelector** : Interface de sélection avec dropdown
- **ModelStatus** : Affichage du statut et des informations du modèle
- **useModelManager** : Hook pour la gestion d'état

### Communication Backend
- **API Routes** : `/api/copilotkit/change-model` et `/api/copilotkit/test-model`
- **Agent LangGraph** : Support dynamique via configuration
- **Persistance** : localStorage + cookies HTTP

### Flux de Données
1. Utilisateur sélectionne un modèle
2. Frontend sauvegarde dans localStorage
3. API route met à jour les cookies
4. Agent LangGraph reçoit la configuration
5. Nouveau modèle utilisé pour les réponses

## 🛠️ Configuration

### Variables d'Environnement
```bash
# OpenRouter (recommandé)
OPENROUTER_API_KEY=sk-or-v1-...

# Alternatives
OPENAI_API_KEY=sk-...
ANTHROPIC_API_KEY=sk-ant-...
```

### Ajout de Nouveaux Modèles
Modifiez `frontend/src/components/ModelSelector.tsx` :

```typescript
export const AI_MODELS: AIModel[] = [
  // ... modèles existants
  {
    id: "nouveau/modele",
    name: "Nouveau Modèle",
    provider: "Provider",
    description: "Description du modèle",
    pricing: "moderate",
    performance: "excellent",
    supportsTools: true,
    contextWindow: "32K",
    icon: "🆕"
  }
];
```

## 🔍 Débogage

### Vérification du Statut
- **Indicateur vert** : Modèle connecté et fonctionnel
- **Indicateur rouge** : Erreur de connexion
- **Indicateur jaune** : Connexion en cours

### Logs
- Console navigateur : Erreurs frontend
- Terminal LangGraph : Erreurs backend
- Network tab : Requêtes API

### Problèmes Courants
1. **Modèle non supporté** : Vérifiez la configuration OpenRouter
2. **Quota dépassé** : Changez vers un modèle gratuit
3. **Erreur de réseau** : Vérifiez la connexion internet

## 📈 Métriques

### Performance
- **Temps de changement** : < 2 secondes
- **Persistance** : Immédiate (localStorage)
- **Synchronisation** : Temps réel

### Utilisation
- **Modèles populaires** : GPT-3.5, Claude Haiku
- **Fréquence de changement** : Trackée via analytics
- **Erreurs** : Monitoring automatique

## 🔮 Améliorations Futures

### Prévues
- [ ] **Auto-sélection** : Choix automatique selon la tâche
- [ ] **Modèles locaux** : Support Ollama/LocalAI
- [ ] **Comparaison** : Interface de comparaison de réponses
- [ ] **Analytics** : Métriques d'utilisation détaillées

### Possibles
- [ ] **Modèles personnalisés** : Fine-tuning utilisateur
- [ ] **Équilibrage de charge** : Rotation automatique
- [ ] **Cache intelligent** : Optimisation des réponses
- [ ] **API externe** : Intégration autres providers

## 🤝 Contribution

Pour ajouter des fonctionnalités :
1. Créez une branche feature
2. Implémentez les changements
3. Testez avec plusieurs modèles
4. Documentez les modifications
5. Créez une pull request

## 📞 Support

- **Documentation** : Ce fichier README
- **Issues** : GitHub Issues
- **Chat** : Discord CopilotKit
- **Email** : <EMAIL>
