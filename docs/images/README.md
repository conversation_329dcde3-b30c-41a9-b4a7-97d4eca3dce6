# 📸 Dossier Images - DroneDelivery

Ce dossier contient toutes les images et captures d'écran utilisées dans la documentation de l'application DroneDelivery.

## 🗂️ Structure

```
images/
├── homepage.png          # Capture d'écran de la page d'accueil
├── catalog.png           # Page du catalogue de drones
├── admin-dashboard.png   # Dashboard administrateur
├── product-detail.png    # Page de détail d'un drone
├── cart.png             # Page du panier
├── checkout.png         # Page de commande
├── profile.png          # Page de profil utilisateur
├── mobile/              # Captures mobiles
│   ├── homepage-mobile.png
│   ├── catalog-mobile.png
│   └── menu-mobile.png
├── features/            # Images des fonctionnalités
│   ├── search-filters.png
│   ├── payment-methods.png
│   └── order-tracking.png
└── logos/               # Logos et icônes
    ├── logo-main.png
    ├── logo-icon.png
    └── favicon.ico
```

## 📋 Guidelines pour les captures d'écran

### Qualité et format
- **Format**: PNG pour les captures d'écran, WEBP pour les photos de produits
- **Résolution**: Minimum 1920x1080 pour desktop, 375x812 pour mobile
- **Compression**: Optimisées pour le web (< 500KB par image)

### Style et cohérence
- **Navigateur**: Chrome en mode navigation privée
- **Zoom**: 100% (pas de zoom navigateur)
- **Données**: Utilisez des données de démonstration cohérentes
- **UI**: Interface en français
- **Thème**: Mode clair par défaut

### Contenu des captures
1. **Homepage**: Showcase des drones populaires, bannière principale
2. **Catalog**: Grille de produits avec filtres visibles
3. **Admin Dashboard**: Statistiques et graphiques principaux
4. **Product Detail**: Galerie d'images, spécifications, avis
5. **Cart**: Produits dans le panier, calculs totaux
6. **Profile**: Onglets de profil, informations utilisateur

## 🎨 Outils recommandés

### Capture d'écran
- **CleanShot X** (macOS) - Annotations et retouches
- **LightShot** (Windows/Linux) - Capture rapide
- **Browser DevTools** - Captures responsive

### Optimisation
- **TinyPNG** - Compression PNG/JPG
- **Squoosh** - Conversion et optimisation web
- **ImageOptim** - Optimisation locale (macOS)

## 🚀 Génération automatique

Pour générer automatiquement les captures d'écran, vous pouvez utiliser :

```bash
# Installation de Playwright pour les captures automatiques
npm install -D playwright
npx playwright install

# Script de capture (à créer)
npm run screenshots
```

## 📝 Checklist avant publication

- [ ] Toutes les images sont optimisées (< 500KB)
- [ ] Les captures reflètent la dernière version de l'UI
- [ ] Pas de données sensibles visibles
- [ ] Style cohérent sur toutes les captures
- [ ] Formats d'image appropriés
- [ ] Alt text descriptif dans le README principal
